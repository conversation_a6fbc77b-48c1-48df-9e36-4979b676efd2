using System;
using System.Windows.Forms;
using ET;

namespace ComboBoxBindingExample
{
    /// <summary>
    /// BindComboBox 方法优化后的使用示例
    /// </summary>
    public partial class ExampleForm : Form
    {
        public ExampleForm()
        {
            InitializeComponent();
        }

        private void ExampleForm_Load(object sender, EventArgs e)
        {
            // 示例1：基本用法（不使用默认值文件）
            // 只使用历史记录功能
            ETForm.BindComboBox(comboBox1, 30, true);

            // 示例2：使用默认值文件
            // 历史值在前面，默认值在后面，重复值只出现一次
            string defaultValuesFile = @"D:\默认值文件\combobox_defaults.txt";
            ETForm.BindComboBox(comboBox2, 30, true, defaultValuesFile);

            // 示例3：指定 historyKey 并使用默认值文件
            ETForm.BindComboBox(comboBox3, "MyCustomKey", 50, false, defaultValuesFile);

            // 示例4：不自动填充最新值，但使用默认值文件
            ETForm.BindComboBox(comboBox4, 20, false, @"C:\config\defaults.txt");
        }

        /// <summary>
        /// 默认值文件格式说明：
        ///
        /// 文件内容示例（每行一个值）：
        /// # 这是注释行，会被忽略
        /// 选项1
        /// 选项2
        /// 选项3
        /// 默认选项A
        /// 默认选项B
        ///
        /// 功能特点：
        /// 1. 历史值和默认值如果重复，只会出现一次
        /// 2. 下拉列表中，历史值在前面，默认值在后面
        /// 3. 以 # 开头的行为注释行，会被忽略
        /// 4. 如果忽略第4个参数或传入null，则没有默认值功能，只显示历史值
        /// 5. 如果默认值文件不存在，不会影响历史记录功能的正常使用
        ///
        /// 编码支持：
        /// 6. 自动检测文件编码格式，支持UTF-8、GBK、GB2312、系统默认编码等
        /// 7. 如果文件出现乱码，会自动尝试多种编码格式进行读取
        /// 8. 推荐使用UTF-8编码保存默认值文件以获得最佳兼容性
        ///
        /// 常见编码问题解决：
        /// - 如果文件显示乱码，请检查文件编码格式
        /// - Windows记事本保存时选择"UTF-8"编码
        /// - VS Code等编辑器默认使用UTF-8编码
        /// - 系统会自动尝试GBK、GB2312等中文编码
        /// </summary>
    }
}
