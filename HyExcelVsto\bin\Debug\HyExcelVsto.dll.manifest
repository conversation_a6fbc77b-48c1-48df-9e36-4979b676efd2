﻿<?xml version="1.0" encoding="utf-8"?>
<asmv1:assembly xsi:schemaLocation="urn:schemas-microsoft-com:asm.v1 assembly.adaptive.xsd" manifestVersion="1.0" xmlns:asmv1="urn:schemas-microsoft-com:asm.v1" xmlns="urn:schemas-microsoft-com:asm.v2" xmlns:asmv2="urn:schemas-microsoft-com:asm.v2" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:co.v1="urn:schemas-microsoft-com:clickonce.v1" xmlns:asmv3="urn:schemas-microsoft-com:asm.v3" xmlns:dsig="http://www.w3.org/2000/09/xmldsig#" xmlns:co.v2="urn:schemas-microsoft-com:clickonce.v2">
  <asmv1:assemblyIdentity name="HyExcelVsto.dll" version="********" publicKeyToken="6dec67056def2682" language="neutral" processorArchitecture="msil" type="win32" />
  <description xmlns="urn:schemas-microsoft-com:asm.v1">HyExcelVsto</description>
  <application />
  <entryPoint>
    <co.v1:customHostSpecified />
  </entryPoint>
  <trustInfo>
    <security>
      <applicationRequestMinimum>
        <PermissionSet Unrestricted="true" ID="Custom" SameSite="site" />
        <defaultAssemblyRequest permissionSetReference="Custom" />
      </applicationRequestMinimum>
      <requestedPrivileges xmlns="urn:schemas-microsoft-com:asm.v3">
        <!--
          UAC 清单选项
          如果要更改 Windows 用户帐户控制级别，请用以下节点之一替换
          requestedExecutionLevel 节点。

        <requestedExecutionLevel  level="asInvoker" uiAccess="false" />
        <requestedExecutionLevel  level="requireAdministrator" uiAccess="false" />
        <requestedExecutionLevel  level="highestAvailable" uiAccess="false" />

          如果要利用文件和注册表虚拟化提供
          向后兼容性，请删除 requestedExecutionLevel 节点。
    -->
        <requestedExecutionLevel level="asInvoker" uiAccess="false" />
      </requestedPrivileges>
    </security>
  </trustInfo>
  <dependency>
    <dependentOS>
      <osVersionInfo>
        <os majorVersion="5" minorVersion="1" buildNumber="2600" servicePackMajor="0" />
      </osVersionInfo>
    </dependentOS>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="Microsoft.Windows.CommonLanguageRuntime" version="4.0.30319.0" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="Microsoft.CSharp" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="Microsoft.VisualBasic" version="10.0.0.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="Microsoft.Win32.Primitives" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="mscorlib" version="*******" publicKeyToken="B77A5C561934E089" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="netstandard" version="2.0.0.0" publicKeyToken="CC7B13FFCD2DDD51" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="stdole" version="7.0.3300.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System" version="*******" publicKeyToken="B77A5C561934E089" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.AppContext" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Collections" version="4.0.11.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Collections.Concurrent" version="4.0.11.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Collections.NonGeneric" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Collections.Specialized" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.ComponentModel" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.ComponentModel.Annotations" version="4.0.10.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.ComponentModel.Composition" version="*******" publicKeyToken="B77A5C561934E089" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.ComponentModel.DataAnnotations" version="*******" publicKeyToken="31BF3856AD364E35" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.ComponentModel.EventBasedAsync" version="4.0.11.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.ComponentModel.Primitives" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.ComponentModel.TypeConverter" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Configuration" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Console" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Core" version="*******" publicKeyToken="B77A5C561934E089" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Data" version="*******" publicKeyToken="B77A5C561934E089" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Data.Common" version="4.2.0.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Data.DataSetExtensions" version="*******" publicKeyToken="B77A5C561934E089" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Data.Entity" version="*******" publicKeyToken="B77A5C561934E089" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Data.Linq" version="*******" publicKeyToken="B77A5C561934E089" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Data.OracleClient" version="*******" publicKeyToken="B77A5C561934E089" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Diagnostics.Contracts" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Diagnostics.Debug" version="4.0.11.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Diagnostics.FileVersionInfo" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Diagnostics.Process" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Diagnostics.StackTrace" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Diagnostics.TextWriterTraceListener" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Diagnostics.Tools" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Diagnostics.TraceSource" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Drawing" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Drawing.Primitives" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Dynamic.Runtime" version="4.0.11.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Globalization" version="4.0.11.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Globalization.Calendars" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Globalization.Extensions" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.IO" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.IO.Compression.ZipFile" version="*******" publicKeyToken="B77A5C561934E089" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.IO.FileSystem" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.IO.FileSystem.DriveInfo" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.IO.FileSystem.Primitives" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.IO.FileSystem.Watcher" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.IO.IsolatedStorage" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.IO.MemoryMappedFiles" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.IO.Pipes" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.IO.UnmanagedMemoryStream" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Linq" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Linq.Expressions" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Linq.Parallel" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Linq.Queryable" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Management" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Net" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Net.Http" version="4.2.0.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Net.Http.Rtc" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Net.NameResolution" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Net.NetworkInformation" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Net.Ping" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Net.Primitives" version="4.0.11.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Net.Requests" version="4.0.11.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Net.Security" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Net.Sockets" version="4.2.0.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Net.WebHeaderCollection" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Net.WebSockets" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Net.WebSockets.Client" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Numerics" version="*******" publicKeyToken="B77A5C561934E089" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.ObjectModel" version="4.0.11.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Reflection" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Reflection.Emit" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Reflection.Emit.ILGeneration" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Reflection.Emit.Lightweight" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Reflection.Extensions" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Reflection.Primitives" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Resources.Reader" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Resources.ResourceManager" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Resources.Writer" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Runtime" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Runtime.Caching" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Runtime.CompilerServices.VisualC" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Runtime.Extensions" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Runtime.Handles" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Runtime.InteropServices" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Runtime.InteropServices.RuntimeInformation" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Runtime.InteropServices.WindowsRuntime" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Runtime.Numerics" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Runtime.Serialization" version="*******" publicKeyToken="B77A5C561934E089" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Runtime.Serialization.Formatters" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Runtime.Serialization.Json" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Runtime.Serialization.Primitives" version="4.2.0.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Runtime.Serialization.Xml" version="4.1.3.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Security" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Security.Claims" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Security.Cryptography.Algorithms" version="4.3.0.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Security.Cryptography.Csp" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Security.Cryptography.Encoding" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Security.Cryptography.Primitives" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Security.Cryptography.X509Certificates" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Security.Principal" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Security.SecureString" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.ServiceModel" version="*******" publicKeyToken="B77A5C561934E089" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.ServiceModel.Duplex" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.ServiceModel.Http" version="4.0.10.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.ServiceModel.NetTcp" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.ServiceModel.Primitives" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.ServiceModel.Security" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.ServiceProcess" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Text.Encoding" version="4.0.11.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Text.Encoding.Extensions" version="4.0.11.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Text.RegularExpressions" version="4.1.1.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Threading" version="4.0.11.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Threading.Overlapped" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Threading.Tasks" version="4.0.11.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Threading.Tasks.Parallel" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Threading.Thread" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Threading.ThreadPool" version="4.0.12.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Threading.Timer" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Transactions" version="*******" publicKeyToken="B77A5C561934E089" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.ValueTuple" version="*******" publicKeyToken="CC7B13FFCD2DDD51" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Web" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Web.DataVisualization" version="*******" publicKeyToken="31BF3856AD364E35" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Web.Extensions" version="*******" publicKeyToken="31BF3856AD364E35" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Windows.Forms" version="*******" publicKeyToken="B77A5C561934E089" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Xml" version="*******" publicKeyToken="B77A5C561934E089" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Xml.Linq" version="*******" publicKeyToken="B77A5C561934E089" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Xml.ReaderWriter" version="4.1.1.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Xml.XDocument" version="4.0.11.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Xml.XmlDocument" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Xml.XmlSerializer" version="4.0.11.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Xml.XPath" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="System.Xml.XPath.XDocument" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="preRequisite" allowDelayedBinding="true">
      <assemblyIdentity name="WindowsBase" version="*******" publicKeyToken="31BF3856AD364E35" language="neutral" />
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="Aliyun.OSS.dll" size="405504">
      <assemblyIdentity name="Aliyun.OSS" version="2.14.1.0" publicKeyToken="0AD4175F0DAC0B9B" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>/jI7ysZy0YWelaGBww+UK3hh59Bv0VsxUVrgPSw9asc=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="AngleSharp.dll" size="960512">
      <assemblyIdentity name="AngleSharp" version="1.3.0.0" publicKeyToken="E83494DCDC6D31EA" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>IMbjixHjnnrTciNvpoS6ox/pkpgaDlwRpZkv3YsVHEs=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="Castle.Core.dll" size="428544">
      <assemblyIdentity name="Castle.Core" version="*******" publicKeyToken="407DD0808D44FBDC" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>0/UhTPsYYRhejzrrwFMSczcdQJ0NEYqHYVoeiIusxGc=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="ExtensionsTools.dll" size="1208832">
      <assemblyIdentity name="ExtensionsTools" version="*******" publicKeyToken="0AA1E866BF1188D6" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>XVCUnY3k9yc4PbRUjEwH8k9JE0z3VuYKdVivm7MDNUI=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="GeoAPI.dll" size="44544">
      <assemblyIdentity name="GeoAPI" version="1.7.5.0" publicKeyToken="A1A0DA7DEF465678" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>NKBFpF9Q0RPy/hUDQPqYrn8+jBCyqkrGFur2RKZz1oc=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="GeoAPI.CoordinateSystems.dll" size="16896">
      <assemblyIdentity name="GeoAPI.CoordinateSystems" version="1.7.5.0" publicKeyToken="A1A0DA7DEF465678" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>sWeQE4XVWR3pSnXIo6QhNR5YsHTSLwz10nmSkMLIfys=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="HyExcelVsto.dll" size="1425408">
      <assemblyIdentity name="HyExcelVsto" version="*******" publicKeyToken="01691588D9FEFED7" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>/b+HJPVa8dehNoR4jb/JJn9/I9n4KBdUSsMWacVaS7w=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="Microsoft.Bcl.AsyncInterfaces.dll" size="26424">
      <assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" version="*******" publicKeyToken="CC7B13FFCD2DDD51" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>j99xhO9XrPDsuUhB3Pf/Lv5IICLTlHSEUshKPy/y6kM=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="Microsoft.Extensions.DependencyInjection.dll" size="97576">
      <assemblyIdentity name="Microsoft.Extensions.DependencyInjection" version="*******" publicKeyToken="ADB9793829DDAE60" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>np+KRVwv257J5NQyoBwihRqa+suqocAnTeRGp5RYsbo=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="Microsoft.Extensions.DependencyInjection.Abstractions.dll" size="64264">
      <assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" version="*******" publicKeyToken="ADB9793829DDAE60" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>GzHkMWm40nSVHPdBUlkjKC7LN6X+KtiB/IibJ23rAFM=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="Microsoft.Extensions.Logging.Abstractions.dll" size="69384">
      <assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" version="*******" publicKeyToken="ADB9793829DDAE60" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>ohfU8DojvewpHgWubm991lJHVBl5Ibt4pnOHLlmtstg=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="Microsoft.Office.Tools.dll" size="19392">
      <assemblyIdentity name="Microsoft.Office.Tools" version="10.0.0.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>L3UyNVA02mZ8DffKXVSyRgEHQ0a56i0jBbJnbZ5XfHo=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="Microsoft.Office.Tools.Common.dll" size="95680">
      <assemblyIdentity name="Microsoft.Office.Tools.Common" version="10.0.0.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>Y3DmCtx0rByK55h8jwWfEGn02KJHJYhqh+p1/TofiVE=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="Microsoft.Office.Tools.Common.v4.0.Utilities.dll" size="32664">
      <assemblyIdentity name="Microsoft.Office.Tools.Common.v4.0.Utilities" version="10.0.0.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>jLCTF8Mm6bD4PDN+rnzN6q0+ReXaNgPh68kMWgatFwI=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="Microsoft.Office.Tools.Excel.dll" size="175552">
      <assemblyIdentity name="Microsoft.Office.Tools.Excel" version="10.0.0.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>IfPKW2hCPFE0v8IwDu3InpTyGc6blBT6bIyPu9PAA9k=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="Microsoft.Office.Tools.v4.0.Framework.dll" size="31680">
      <assemblyIdentity name="Microsoft.Office.Tools.v4.0.Framework" version="10.0.0.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>N4bOLABmWN0w9ckZ5WHUvr9dArzfynyC5RCMVRiE2/M=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="Microsoft.VisualStudio.Tools.Applications.Runtime.dll" size="86464">
      <assemblyIdentity name="Microsoft.VisualStudio.Tools.Applications.Runtime" version="10.0.0.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>d9mXRzBqhUbn5au7DNJRVSmV9rl6z1CLCQzVziV30Kc=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="Microsoft.Web.Infrastructure.dll" size="50648">
      <assemblyIdentity name="Microsoft.Web.Infrastructure" version="2.0.0.0" publicKeyToken="31BF3856AD364E35" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>s+1ZotImVm8t+4gCh8mfQXJk0MPyQSQfqnvoPBvYjKg=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="Microsoft.Web.WebView2.Core.dll" size="632432">
      <assemblyIdentity name="Microsoft.Web.WebView2.Core" version="1.0.3240.44" publicKeyToken="2A8AB48044D2601E" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>fCaa8rb/aQvLZdeBLbgoQxErm8Dc31swTpklWZaowbQ=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="Microsoft.Web.WebView2.WinForms.dll" size="38992">
      <assemblyIdentity name="Microsoft.Web.WebView2.WinForms" version="1.0.3240.44" publicKeyToken="2A8AB48044D2601E" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>T11hljajEhv181zF0RERlr5iri71aDGM0HucAsP3o7k=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="Microsoft.Win32.Registry.dll" size="26496">
      <assemblyIdentity name="Microsoft.Win32.Registry" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>6anSgcGnCKquNm+C/WoXQvZdopGMxPpeqqraC+JCd9k=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="NetTopologySuite.dll" size="903168">
      <assemblyIdentity name="NetTopologySuite" version="2.0.0.0" publicKeyToken="F580A05016EBADA1" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>VSiFE73ZqDQJj8SplfX5bZm5RW8aJsE0/gBRCEmD4TQ=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="Newtonsoft.Json.dll" size="711952">
      <assemblyIdentity name="Newtonsoft.Json" version="1*******" publicKeyToken="30AD4FE6B2A6AEED" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>4eJ697B+7t9c5xqSVfBCKBam/FhJpIPGcU4bRyBE+p0=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="OpenAI.dll" size="2239488">
      <assemblyIdentity name="OpenAI" version="*******" publicKeyToken="B4187F3E65366280" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>b5q77t1fRDRX4Wmfm/3gEfBsWVfLZIuDKALrleY4bRU=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="ServiceStack.dll" size="5335552">
      <assemblyIdentity name="ServiceStack" version="*******" publicKeyToken="02C12CBDA47E6587" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>1AJUKjuOyM8nXEDtv1ybJ3MflwY9RWX+r947+V/aeJ8=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="ServiceStack.Client.dll" size="498176">
      <assemblyIdentity name="ServiceStack.Client" version="*******" publicKeyToken="02C12CBDA47E6587" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>5+/2PbPMA3Cci5OkM4AJVaTqQH3M86YjNtc93ia7c9w=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="ServiceStack.Common.dll" size="1027584">
      <assemblyIdentity name="ServiceStack.Common" version="*******" publicKeyToken="02C12CBDA47E6587" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>lZLK88YJruA/8/TFUIR7guUFCYoBHw/7mcxvA9nXnkE=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="ServiceStack.Interfaces.dll" size="421376">
      <assemblyIdentity name="ServiceStack.Interfaces" version="*******" publicKeyToken="02C12CBDA47E6587" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>I9X7qYH9zxDOO17wbRZY2+qnRPd827XXMGRpDrdS8CA=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="ServiceStack.Text.dll" size="698880">
      <assemblyIdentity name="ServiceStack.Text" version="*******" publicKeyToken="02C12CBDA47E6587" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>qlz6tXsMAFD7N1oUwkZUH7JyKbQh4Z8YCnrpdBFqh0Q=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="SharpCompress.dll" size="614912">
      <assemblyIdentity name="SharpCompress" version="0.40.0.0" publicKeyToken="AFB0A02973931D96" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>93DkgQUmfrkfP0RWClSpNoyYJdhdooY6i4XTrMSl/kU=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="System.Buffers.dll" size="23816">
      <assemblyIdentity name="System.Buffers" version="*******" publicKeyToken="CC7B13FFCD2DDD51" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>LXjXcMnLmXGZFUrowBi58dHvvIZyn3Jk3ebbrSoSysM=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="System.ClientModel.dll" size="170528">
      <assemblyIdentity name="System.ClientModel" version="1.4.1.0" publicKeyToken="92742159E12E44C8" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>lNyUN5e5/nHpJowM+gDf0rIKMFpFT7coZA662c0Kfyw=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="System.Collections.Immutable.dll" size="259368">
      <assemblyIdentity name="System.Collections.Immutable" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>B6HpDZHg+nrO/VwO5KPb5DQaw8tagievYodvQnYenOE=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="System.Diagnostics.DiagnosticSource.dll" size="199944">
      <assemblyIdentity name="System.Diagnostics.DiagnosticSource" version="*******" publicKeyToken="CC7B13FFCD2DDD51" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>jG/cZeKHtqHNcwVtRHG5hc1iy21YZYPhuIm6Q6I+YeY=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="System.Drawing.Common.dll" size="50440">
      <assemblyIdentity name="System.Drawing.Common" version="9.0.0.0" publicKeyToken="CC7B13FFCD2DDD51" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>M0mgsrBQn9nZdGOONcIRfdkORZ9eAt9jaYAMwgURpC4=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="System.IO.Pipelines.dll" size="84776">
      <assemblyIdentity name="System.IO.Pipelines" version="*******" publicKeyToken="CC7B13FFCD2DDD51" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>8Z14xc/93q5mp513L6DrswwQGRL+fPdUGRMwi1TPec8=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="System.Memory.dll" size="145200">
      <assemblyIdentity name="System.Memory" version="*******" publicKeyToken="CC7B13FFCD2DDD51" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>1ejkhm+c+mb3dlZg+EshAZiJPlUzVIev5evaNCwOkT0=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="System.Memory.Data.dll" size="35624">
      <assemblyIdentity name="System.Memory.Data" version="*******" publicKeyToken="CC7B13FFCD2DDD51" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>p3xwGzQpesEqr5kkMqMtWzN8Pv3M1P/MK2E8RxQMWSg=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="System.Numerics.Vectors.dll" size="110344">
      <assemblyIdentity name="System.Numerics.Vectors" version="4.1.6.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>IML6gbjHDWUQmddilU8oX9T5QuY7LXIXwUXauNSy9Mk=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="System.Runtime.CompilerServices.Unsafe.dll" size="19256">
      <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" version="6.0.3.0" publicKeyToken="B03F5F7F11D50A3A" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>CMvXJ4tm8eaEJagtS5cYGkEw2T492RgxQHq6chLM2s8=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="System.Security.AccessControl.dll" size="37136">
      <assemblyIdentity name="System.Security.AccessControl" version="6.0.0.1" publicKeyToken="B03F5F7F11D50A3A" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>jnHgnBonBnBDM9OtIOKGrORByLUS3S27bcaVQNrsVMg=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="System.Security.Principal.Windows.dll" size="18312">
      <assemblyIdentity name="System.Security.Principal.Windows" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>tNjhWtwjXQ6Fjjm1Ez5dAKS6qMlPTznjteeRsPnAyAY=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="System.Text.Encoding.CodePages.dll" size="766248">
      <assemblyIdentity name="System.Text.Encoding.CodePages" version="*******" publicKeyToken="B03F5F7F11D50A3A" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>IPJPozVL7r8/7iUbdF+lig45nK9aRhxkGylB/dcoaqk=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="System.Text.Encodings.Web.dll" size="79656">
      <assemblyIdentity name="System.Text.Encodings.Web" version="*******" publicKeyToken="CC7B13FFCD2DDD51" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>/bHl62yjCAjl0tSXmbhRB9t3s3wVF7LK03qPh31faso=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="System.Text.Json.dll" size="726816">
      <assemblyIdentity name="System.Text.Json" version="*******" publicKeyToken="CC7B13FFCD2DDD51" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>ZsIJ56T1Y7sWqmdFkfOX7YuzKc+nKQXP3lsBgATOUi8=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="System.Threading.Channels.dll" size="78600">
      <assemblyIdentity name="System.Threading.Channels" version="*******" publicKeyToken="CC7B13FFCD2DDD51" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>WiBF+D9U8AIw/p/Jp7Vi2WOjkpr15QrPa/jYYdH7PoA=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="System.Threading.Tasks.Extensions.dll" size="27960">
      <assemblyIdentity name="System.Threading.Tasks.Extensions" version="4.2.4.0" publicKeyToken="CC7B13FFCD2DDD51" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>r04T70GP2JtDLeIM0d+3+hCnF5yN0EnJhWMW5QxZSco=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="System.ValueTuple.dll" size="25232">
      <assemblyIdentity name="System.ValueTuple" version="*******" publicKeyToken="CC7B13FFCD2DDD51" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>6QXRAlhbIsbfBPIZr1y9v6e8Fll56XiLYt9tzBZeEPQ=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="System.Web.Helpers.dll" size="138168">
      <assemblyIdentity name="System.Web.Helpers" version="*******" publicKeyToken="31BF3856AD364E35" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>ZlZNKhkOb9JI08JjMDBwBmzNU8LQJqYW4y/CHcqCbu8=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="System.Web.Mvc.dll" size="548800">
      <assemblyIdentity name="System.Web.Mvc" version="5.3.0.0" publicKeyToken="31BF3856AD364E35" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>CPj6Dpa1Utzwidso3fvGP1wEwtIyx/8GRT5P/Qq8i4M=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="System.Web.Razor.dll" size="265248">
      <assemblyIdentity name="System.Web.Razor" version="*******" publicKeyToken="31BF3856AD364E35" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>x23EdEVW7fvGlNvluT5JZuJpBisP/R90ZgzC6vZNQ5Q=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="System.Web.WebPages.dll" size="207384">
      <assemblyIdentity name="System.Web.WebPages" version="*******" publicKeyToken="31BF3856AD364E35" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>0OtFfESGieOOkHn9dwCipuX/Yo7aLPGZC/FCQfhxFPs=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="System.Web.WebPages.Deployment.dll" size="43960">
      <assemblyIdentity name="System.Web.WebPages.Deployment" version="*******" publicKeyToken="31BF3856AD364E35" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>1ilPDWlrAqr8rPz2OI6pZL2SbXLWJWzUUT4jtoV4zKA=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="System.Web.WebPages.Razor.dll" size="41912">
      <assemblyIdentity name="System.Web.WebPages.Razor" version="*******" publicKeyToken="31BF3856AD364E35" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>4w5Wrc+C26jzQMjCOHcipHyaRgGn/dWJeaSqKEbC58s=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <dependency>
    <dependentAssembly dependencyType="install" allowDelayedBinding="true" codebase="ZstdSharp.dll" size="427520">
      <assemblyIdentity name="ZstdSharp" version="*******" publicKeyToken="8D151AF33A4AD5CF" language="neutral" processorArchitecture="msil" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>5puqUH2qX7R+vP3TMl6z9HzPZS6qY+YYElLnqZDpsQo=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
  <file name="config\.data\FileReceiver.txt" size="173">
    <hash>
      <dsig:Transforms>
        <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
      </dsig:Transforms>
      <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
      <dsig:DigestValue>JLQDPMFKc1uPfsgVzj0WLbs17MmJG5cBuinYlDkkbFg=</dsig:DigestValue>
    </hash>
  </file>
  <file name="HyExcelVsto.dll.config" size="17738">
    <hash>
      <dsig:Transforms>
        <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
      </dsig:Transforms>
      <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
      <dsig:DigestValue>xovwk1kPJQSMzFO3BZ4DSoq8YS+3D1hwpAqjFUCs010=</dsig:DigestValue>
    </hash>
  </file>
  <file name="Images\api.png" size="272627">
    <hash>
      <dsig:Transforms>
        <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
      </dsig:Transforms>
      <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
      <dsig:DigestValue>AxaxxV4JZKp7LGcwLyiTN9vS6m6SaQeUinZs//YoZFI=</dsig:DigestValue>
    </hash>
  </file>
  <file name="Images\office.png" size="249285">
    <hash>
      <dsig:Transforms>
        <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
      </dsig:Transforms>
      <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
      <dsig:DigestValue>mOoj2eEqwkjMv/XTgJHTLUQcjtIjaZmgSwtNLt2s7ac=</dsig:DigestValue>
    </hash>
  </file>
  <file name="Images\panel.png" size="3904">
    <hash>
      <dsig:Transforms>
        <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
      </dsig:Transforms>
      <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
      <dsig:DigestValue>BAB3QEMuLIXbPYE6RQPaohArQO2eZLNwepMlhHcHY5Q=</dsig:DigestValue>
    </hash>
  </file>
  <file name="Images\run.png" size="14967">
    <hash>
      <dsig:Transforms>
        <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
      </dsig:Transforms>
      <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
      <dsig:DigestValue>PZ35DUM8bz9AgncBsfxAebfmmnZD1YbnUWNVA0jUsw0=</dsig:DigestValue>
    </hash>
  </file>
  <file name="Images\unlock.png" size="6757">
    <hash>
      <dsig:Transforms>
        <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
      </dsig:Transforms>
      <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
      <dsig:DigestValue>1P3WJiYkE8Y4RkySK1eW/eAzOeKILbe16BMgKTC6bYA=</dsig:DigestValue>
    </hash>
  </file>
  <file name="Microsoft.DiaSymReader.Native.amd64.dll" size="1495800">
    <hash>
      <dsig:Transforms>
        <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
      </dsig:Transforms>
      <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
      <dsig:DigestValue>lfMBAS7QnAnJ62GiOigDpwQ+ajP5yJV3RjebYQpSl40=</dsig:DigestValue>
    </hash>
  </file>
  <file name="Microsoft.DiaSymReader.Native.x86.dll" size="1188080">
    <hash>
      <dsig:Transforms>
        <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
      </dsig:Transforms>
      <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
      <dsig:DigestValue>8h2p+4MayUNzYTW27hCaSzUlEbjWwHywPGa2GZbR3ck=</dsig:DigestValue>
    </hash>
  </file>
  <vstav3:addIn xmlns:vstav3="urn:schemas-microsoft-com:vsta.v3">
    <vstav3:entryPointsCollection>
      <vstav3:entryPoints>
        <vstav3:entryPoint class="HyExcelVsto.ThisAddIn">
          <assemblyIdentity name="HyExcelVsto" version="*******" publicKeyToken="01691588D9FEFED7" language="neutral" processorArchitecture="msil" />
        </vstav3:entryPoint>
      </vstav3:entryPoints>
    </vstav3:entryPointsCollection>
    <vstav3:update enabled="false" />
    <vstav3:application>
      <vstov4:customizations xmlns:vstov4="urn:schemas-microsoft-com:vsto.v4">
        <vstov4:customization>
          <vstov4:appAddIn application="Excel" loadBehavior="3" keyName="HyExcelVsto">
            <vstov4:friendlyName>HyExcelVsto</vstov4:friendlyName>
            <vstov4:description>HyExcelVsto</vstov4:description>
            <vstov4.1:ribbonTypes xmlns:vstov4.1="urn:schemas-microsoft-com:vsto.v4.1">
              <vstov4.1:ribbonType name="HyExcelVsto.HyRibbonClass, HyExcelVsto, Version=*******, Culture=neutral, PublicKeyToken=01691588d9fefed7" />
            </vstov4.1:ribbonTypes>
          </vstov4:appAddIn>
        </vstov4:customization>
      </vstov4:customizations>
    </vstav3:application>
  </vstav3:addIn>
<publisherIdentity name="CN=DESKTOP-QUE4EMJ\HHY" issuerKeyHash="76f8fef03be9e916fdc6442164bff7e6db87a865" /><Signature Id="StrongNameSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>PEy6+G+6vBOrQl2WnQL2RuemtRXiyycQjbMPjRU/PGA=</DigestValue></Reference></SignedInfo><SignatureValue>iDdLeMPR6PfcBzCX2x7UA/vPRtDdEtQ9mcD1B1MhjSJ9hykMbycoIhkhSrzwZf80xxxbikX3E64dSrR4BbOfYD+VwZ1rcbCLAHS7gmlLSLfuZWhPRjGjych56oo393taHbYilQeupziCrqlThfe6RbWJRYvl31Ss9jTKK2WL+2o=</SignatureValue><KeyInfo Id="StrongNameKeyInfo"><KeyValue><RSAKeyValue><Modulus>xYfYzcC3fdVJvs7+69QTUGhjXJi3gWoDsTS8KXwn8LL2lMZcQ78hC0D+ZDyoSo72CNR6S8FPDTl34NlsQoT9V4n7DGFPbK4b99u5gcvVUk03T2p9l+UhznPZ6ibKP0BwHwi6z7E7GfuRRSwxDRohtkeN1B8mqLSpfq7Ldi9ERs0=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><msrel:RelData xmlns:msrel="http://schemas.microsoft.com/windows/rel/2005/reldata"><r:license xmlns:r="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:as="http://schemas.microsoft.com/windows/pki/2005/Authenticode"><r:grant><as:ManifestInformation Hash="603c3f158d0fb38d1027cbe215b5a6e746f6029d965d42ab13bcba6ff8ba4c3c" Description="" Url=""><as:assemblyIdentity name="HyExcelVsto.dll" version="********" publicKeyToken="6dec67056def2682" language="neutral" processorArchitecture="msil" type="win32" /></as:ManifestInformation><as:SignedBy /><as:AuthenticodePublisher><as:X509SubjectName>CN=DESKTOP-QUE4EMJ\HHY</as:X509SubjectName></as:AuthenticodePublisher></r:grant><r:issuer><Signature Id="AuthenticodeSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>FNT4i1mOMAHUiNxyZDaIwnJC6IVu2FxAEfjc5/1WLRc=</DigestValue></Reference></SignedInfo><SignatureValue>NAi9+lAqCnmGRLULiqSiZY3HzrCS6dFOp/iPiVtY71TEmyKPRYmbGotI9DZiJ0h29ydYju9rUzmoOe1BtKBMc6R/+DaWRUpkGgDMlpckLKUdcK6UmFPOyWKeRB7i2CWa+tdSCd58ye4uSBqcmQsuVFDYYiOv5S21+dl6a/UfkoU=</SignatureValue><KeyInfo><KeyValue><RSAKeyValue><Modulus>xYfYzcC3fdVJvs7+69QTUGhjXJi3gWoDsTS8KXwn8LL2lMZcQ78hC0D+ZDyoSo72CNR6S8FPDTl34NlsQoT9V4n7DGFPbK4b99u5gcvVUk03T2p9l+UhznPZ6ibKP0BwHwi6z7E7GfuRRSwxDRohtkeN1B8mqLSpfq7Ldi9ERs0=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><X509Data><X509Certificate>MIIB5TCCAU6gAwIBAgIQKuMtYyMoKIdEv4fC44j4tDANBgkqhkiG9w0BAQsFADAxMS8wLQYDVQQDHiYARABFAFMASwBUAE8AUAAtAFEAVQBFADQARQBNAEoAXABIAEgAWTAeFw0yNTA2MTcwODA4MTJaFw0yNjA2MTcxNDA4MTJaMDExLzAtBgNVBAMeJgBEAEUAUwBLAFQATwBQAC0AUQBVAEUANABFAE0ASgBcAEgASABZMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDFh9jNwLd91Um+zv7r1BNQaGNcmLeBagOxNLwpfCfwsvaUxlxDvyELQP5kPKhKjvYI1HpLwU8NOXfg2WxChP1XifsMYU9srhv327mBy9VSTTdPan2X5SHOc9nqJso/QHAfCLrPsTsZ+5FFLDENGiG2R43UHyaotKl+rst2L0RGzQIDAQABMA0GCSqGSIb3DQEBCwUAA4GBACra0QfchCVF4U3hVlokQR8jrULaqburZnwTAzXudUBhelLojyfaUc7aOyMjS+fLsqfqVY8DY9N5em/YreXeTruaT57312t4c86BCkjqskQTJcBJmZaAzjD4oAhfXK6SK+RtiEUiVR6/q9vCzy9FwYadj9LV2RPbmU3D7zdywQsU</X509Certificate></X509Data></KeyInfo></Signature></r:issuer></r:license></msrel:RelData></KeyInfo></Signature></asmv1:assembly>