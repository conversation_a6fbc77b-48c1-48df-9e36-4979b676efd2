using System;
using ET;

namespace ETFileEncodingExample
{
    /// <summary>
    /// ETFile.ReadAllLinesWithEncoding 方法使用示例
    /// </summary>
    public class EncodingExample
    {
        /// <summary>
        /// 演示智能编码检测读取文件的各种用法
        /// </summary>
        public static void DemonstrateEncodingDetection()
        {
            try
            {
                // 示例1：读取可能包含中文的配置文件
                string configFile = @"C:\config\settings.txt";
                string[] configLines = ETFile.ReadAllLinesWithEncoding(configFile);
                
                Console.WriteLine($"成功读取配置文件，共 {configLines.Length} 行");
                foreach (string line in configLines)
                {
                    if (!string.IsNullOrWhiteSpace(line) && !line.StartsWith("#"))
                    {
                        Console.WriteLine($"配置项: {line}");
                    }
                }

                // 示例2：读取ComboBox默认值文件
                string defaultValuesFile = @"D:\data\combobox_defaults.txt";
                string[] defaultValues = ETFile.ReadAllLinesWithEncoding(defaultValuesFile);
                
                Console.WriteLine($"\n默认值文件内容：");
                foreach (string value in defaultValues)
                {
                    if (!string.IsNullOrWhiteSpace(value) && !value.StartsWith("#"))
                    {
                        Console.WriteLine($"- {value}");
                    }
                }

                // 示例3：读取可能包含各种编码的日志文件
                string logFile = @"C:\logs\application.log";
                string[] logLines = ETFile.ReadAllLinesWithEncoding(logFile);
                
                Console.WriteLine($"\n日志文件最后10行：");
                int startIndex = Math.Max(0, logLines.Length - 10);
                for (int i = startIndex; i < logLines.Length; i++)
                {
                    Console.WriteLine($"[{i + 1}] {logLines[i]}");
                }

                // 示例4：处理超长路径的文件
                string longPathFile = @"C:\very\long\path\that\exceeds\normal\windows\path\limits\data.txt";
                string[] longPathLines = ETFile.ReadAllLinesWithEncoding(longPathFile);
                
                Console.WriteLine($"\n超长路径文件读取成功，共 {longPathLines.Length} 行");

            }
            catch (Exception ex)
            {
                Console.WriteLine($"读取文件时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 演示在ComboBox绑定中的使用
        /// </summary>
        public static void DemonstrateComboBoxUsage()
        {
            // 这是ETForm.BindComboBox内部使用ETFile.ReadAllLinesWithEncoding的示例
            
            string defaultValuesFile = @"C:\config\combobox_defaults.txt";
            
            // 直接使用ETFile方法读取默认值
            string[] defaultValues = ETFile.ReadAllLinesWithEncoding(defaultValuesFile);
            
            Console.WriteLine("ComboBox默认值：");
            foreach (string value in defaultValues)
            {
                string trimmedValue = value?.Trim();
                if (!string.IsNullOrWhiteSpace(trimmedValue) && !trimmedValue.StartsWith("#"))
                {
                    Console.WriteLine($"- {trimmedValue}");
                }
            }
        }

        /// <summary>
        /// 演示错误处理和日志记录
        /// </summary>
        public static void DemonstrateErrorHandling()
        {
            // 测试不存在的文件
            string nonExistentFile = @"C:\does\not\exist.txt";
            string[] result = ETFile.ReadAllLinesWithEncoding(nonExistentFile);
            
            Console.WriteLine($"不存在文件的处理结果: {result.Length} 行");
            
            // 测试无权限访问的文件
            string restrictedFile = @"C:\Windows\System32\config\SAM";
            string[] restrictedResult = ETFile.ReadAllLinesWithEncoding(restrictedFile);
            
            Console.WriteLine($"受限文件的处理结果: {restrictedResult.Length} 行");
        }
    }

    /// <summary>
    /// 程序入口点
    /// </summary>
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== ETFile.ReadAllLinesWithEncoding 使用示例 ===\n");
            
            // 演示基本用法
            EncodingExample.DemonstrateEncodingDetection();
            
            Console.WriteLine("\n" + new string('=', 50) + "\n");
            
            // 演示ComboBox中的使用
            EncodingExample.DemonstrateComboBoxUsage();
            
            Console.WriteLine("\n" + new string('=', 50) + "\n");
            
            // 演示错误处理
            EncodingExample.DemonstrateErrorHandling();
            
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
    }
}

/*
方法特性总结：

1. 智能编码检测：
   - 自动尝试UTF-8、GBK、GB2312、系统默认编码等
   - 通过乱码检测算法选择最合适的编码

2. 超长路径支持：
   - 自动添加长路径前缀处理超过260字符的路径
   - 与ETFile的其他方法保持一致的长路径支持

3. 完善的错误处理：
   - 文件不存在时返回空数组而不是抛出异常
   - 详细的日志记录，便于问题诊断

4. 性能优化：
   - 按编码优先级顺序尝试，提高成功率
   - 智能乱码检测，避免误判

5. 易于使用：
   - 公共静态方法，可在任何地方调用
   - 与File.ReadAllLines相同的接口，易于替换
*/
