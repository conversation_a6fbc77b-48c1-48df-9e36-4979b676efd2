using Microsoft.Office.Core;
using Microsoft.Office.Interop.Excel;
using Microsoft.Office.Tools.Ribbon;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows.Forms;
using Application = Microsoft.Office.Interop.Excel.Application;
using Button = System.Windows.Forms.Button;
using CheckBox = System.Windows.Forms.CheckBox;
using ListBox = System.Windows.Forms.ListBox;
using ScrollBars = System.Windows.Forms.ScrollBars;
using TextBox = System.Windows.Forms.TextBox;

namespace ET
{
    /// <summary>
    /// 窗体操作扩展工具类，提供Excel VSTO开发中的窗体相关功能
    /// </summary>
    /// <remarks>
    /// 主要功能包括：
    /// 1. 窗体消息传递和窗体操作（隐藏、显示、定位等）
    /// 2. Excel单元格坐标获取和屏幕坐标转换
    /// 3. Excel窗体操作和窗口句柄管理
    /// 4. 控件数据绑定和配置文件操作
    /// 5. 控件填充和属性设置（跨线程安全）
    /// 6. Excel菜单操作和日志记录功能
    /// 7. Win32 API封装和窗体样式设置
    /// </remarks>
    public static partial class ETForm
    {
        /// <summary>
        /// ComboBox控件最大显示项目数量
        /// </summary>
        private const int MaxComboBoxItems = 40;

        /// <summary>
        /// 选项分隔符，用于配置文件中多个选项的分隔
        /// </summary>
        private const char OptionsSeparator = '|';

        /// <summary>
        /// 当前活动的Excel窗口句柄
        /// </summary>
        public static IntPtr ActiveExcelIntPtr;

        /// <summary>
        /// 上一个Excel窗口句柄
        /// </summary>
        public static IntPtr PreviousExcelIntPtr;

        /// <summary>
        /// Excel应用程序实例
        /// </summary>
        public static Application XlApp;

        #region 窗体消息传递

        /// <summary>
        /// 向指定窗体发送消息
        /// </summary>
        /// <param name="fromName">目标窗体的标题名称</param>
        /// <param name="message">要发送的消息内容</param>
        /// <remarks>使用Windows API的WM_COPYDATA消息机制在不同窗体间传递字符串数据。 如果找不到目标窗体，消息发送将被忽略。</remarks>
        public static void SendMessageToWindow(string fromName, string message)
        {
            // 根据窗体标题查找目标窗体句柄
            IntPtr hWnd = ETFormNative.FindWindow(null, fromName);
            if (hWnd.ToInt32() == 0)
            {
                // 未找到目标窗体，忽略消息发送
                //Debug.Print("SendString:未找到消息接受者！");
            }
            else
            {
                // 构造COPYDATASTRUCT结构体并发送消息
                byte[] sarr = Encoding.Default.GetBytes(message);
                int len = sarr.Length;
                COPYDATASTRUCT cds;
                cds.dwData = (IntPtr)0;
                cds.cbData = len + 1;
                cds.lpData = message;
                ETFormNative.SendMessage(hWnd, WinAPI.WM_COPYDATA, 0, ref cds);
            }
        }

        #endregion 窗体消息传递

        #region 窗体操作

        /// <summary>
        /// 将窗体隐藏到系统托盘（Hide方法的别名）
        /// </summary>
        /// <param name="frm">要隐藏的窗体</param>
        public static void HideToTray(this Form frm)
        { Hide(frm); }

        /// <summary>
        /// 隐藏窗体并从任务栏移除
        /// </summary>
        /// <param name="frm">要隐藏的窗体</param>
        /// <remarks>此方法会将窗体设置为不可见状态，并从任务栏中移除显示</remarks>
        public static void Hide(this Form frm)
        {
            frm.Visible = false;
            frm.ShowInTaskbar = false;
        }

        /// <summary>
        /// 在窗体关闭事件中将窗体隐藏到托盘而不是真正关闭
        /// </summary>
        /// <param name="frm">要隐藏的窗体</param>
        /// <param name="formClosingEventArgs">窗体关闭事件参数</param>
        /// <remarks>此方法会取消窗体关闭操作，转而将窗体隐藏到托盘</remarks>
        public static void HideToTray(this Form frm, FormClosingEventArgs formClosingEventArgs)
        {
            formClosingEventArgs.Cancel = true;
            frm.Visible = false;
            frm.ShowInTaskbar = false;
        }

        /// <summary>
        /// 将窗体恢复为正常显示状态
        /// </summary>
        /// <param name="frm">要恢复显示的窗体</param>
        /// <remarks>此方法会将窗体设置为正常窗口状态，在任务栏显示，并设置为可见</remarks>
        public static void NormalWindowState(this Form frm)
        {
            frm.ShowInTaskbar = true;
            frm.WindowState = FormWindowState.Normal;
            frm.Visible = true;
        }

        /// <summary>
        /// 将窗体定位到屏幕右下角
        /// </summary>
        /// <param name="form">要定位的窗体</param>
        /// <param name="offsetX">距离右边界的偏移量(默认50像素)</param>
        /// <param name="offsetY">距离下边界的偏移量(默认50像素)</param>
        /// <remarks>此方法会将窗体放置在屏幕右下角位置,可通过offsetX和offsetY参数调整具体位置</remarks>
        public static void PositionFormAtScreenBottomRight(Form form, int offsetX = 50, int offsetY = 50)
        {
            try
            {
                if (form == null)
                    return;

                // 获取主屏幕尺寸
                System.Drawing.Rectangle screen = Screen.PrimaryScreen.Bounds;

                // 计算窗体位置
                form.Left = screen.Width - form.Width - offsetX;
                form.Top = screen.Height - form.Height - offsetY;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
            }
        }

        public static void NoActivateStyle(Form form)
        {
            if (form == null)
                return;
            IntPtr style = new(GetWindowLongPtr(form.Handle, GWL_EXSTYLE).ToInt64() | WS_EX_NOACTIVATE);
            SetWindowLongPtr(form.Handle, GWL_EXSTYLE, style);
        }

        // 此函数用于设置窗体为Excel子窗体，并修改样式避免焦点抢夺
        public static void AsChildWindow(Form form, IntPtr intPtr)
        {
            //Debug.Print("SetAsChildWindow");
            SetParent(form.Handle, intPtr); // 设置窗体为Excel的子窗体
        }

        #endregion 窗体操作

        #region 获得单元格坐标

        //public static double Zoom = 100;
        //public static long PPI;

        /// <summary>
        /// 获取工作区 RECT
        /// </summary>
        public static WinAPI.RECT WorksheetWorkingAreaRect(Application xlApp)
        {
            string xlAppCaption = xlApp.Caption;

            WinAPI.RECT xl7Rect = new();
            WinAPI.RECT hScrBarRect = new();
            WinAPI.RECT vScrBarRect = new();

            // 先获得工作区Rect
            IntPtr hWndWin = WinAPI.FindWindow("XLMAIN", xlAppCaption); // Excel主程序窗口句柄

            IntPtr lHwndXlDesk = WinAPI.FindWindowEx(hWndWin, IntPtr.Zero, "XLDESK", null);
            IntPtr lHwndExcel7 = WinAPI.FindWindowEx(lHwndXlDesk, IntPtr.Zero, "EXCEL7", null);
            WinAPI.GetWindowRect(lHwndExcel7, ref xl7Rect);

            IntPtr lhwndHscrBar = WinAPI.FindWindowEx(lHwndExcel7, IntPtr.Zero, "NUIScrollbar", "水平");
            IntPtr lhwndVscrBar = WinAPI.FindWindowEx(lHwndExcel7, IntPtr.Zero, "NUIScrollbar", "垂直");

            Window activeWindow = xlApp.ActiveWindow;
            if (activeWindow.Split)
            {
                if (activeWindow.SplitRow > 0)
                    lhwndVscrBar = WinAPI.FindWindowEx(lHwndExcel7, lhwndVscrBar, "NUIScrollbar", "垂直"); // 垂直滚动条的句柄

                if (activeWindow.SplitColumn > 0)
                    lhwndHscrBar = WinAPI.FindWindowEx(lHwndExcel7, lhwndHscrBar, "NUIScrollbar", "水平"); // 水平滚动条的句柄
            }

            WinAPI.GetWindowRect(lhwndHscrBar, ref hScrBarRect);
            WinAPI.GetWindowRect(lhwndVscrBar, ref vScrBarRect);

            if (activeWindow.DisplayHorizontalScrollBar || activeWindow.DisplayWorkbookTabs)
                xl7Rect.Bottom = vScrBarRect.Bottom;
            if (activeWindow.DisplayVerticalScrollBar)
                xl7Rect.Right = hScrBarRect.Right - 2;

            Pane pane = activeWindow.Panes[1];
            dynamic visibleRange = pane.VisibleRange.Cells[1, 1];

            xl7Rect.Left = pane.PointsToScreenPixelsX(0) + PointsToScreenPixels(visibleRange.Left);
            xl7Rect.Top = pane.PointsToScreenPixelsY(0) + PointsToScreenPixels(visibleRange.Top);

            return xl7Rect;
        }

        /// <summary>
        /// 获取Excel范围的屏幕坐标信息
        /// </summary>
        public static WinAPI.RECT RangeRect(Application xlApp, Range inputRange)
        {
            WinAPI.RECT windowRect = new();

            IntPtr windowHandle = new(xlApp.ActiveWindow.Hwnd);
            ETFormNative.GetWindowRect(windowHandle, ref windowRect);

            Window window = xlApp.ActiveWindow;

            bool isEntireColumnSelected = inputRange.Columns.Count >= xlApp.Columns.Count;
            bool isEntireRowSelected = inputRange.Rows.Count >= xlApp.Rows.Count;

            // 如果选择的是整行或整列，设置完整的左、右、上和下边界
            if (isEntireRowSelected || isEntireColumnSelected)
            {
                //这里不知道原来啥思路，先注释掉
                //if (isEntireRowSelected)
                //{
                //    windowRect.Left = windowRect.Left;
                //    windowRect.Right = windowRect.Right;
                //}
                //if (isEntireColumnSelected)
                //{
                //    windowRect.Top = windowRect.Top;
                //    windowRect.Bottom = windowRect.Bottom;
                //}
                return windowRect;
            }

            // 如果不是整列或整行，则继续获取选区的具体坐标 获取当前活动窗口的第一个面板，理论上ActiveWindow至少有一个面板
            Pane pane = window.Panes[1];
            bool isRangeVisible = false;
            WinAPI.RECT rangeRect = new();

            // 循环检查所有面板，看是否有板显示了inputRange
            for (int j = 1; j <= window.Panes.Count; j++)
            {
                pane = window.Panes[j];
                Range oRngVsbl = pane.VisibleRange;
                Range range = xlApp.Intersect(oRngVsbl, inputRange);

                // 如果有交集，说明inputRange在当前面板是可见的
                if (range != null)
                {
                    isRangeVisible = true;
                    rangeRect.Top = pane.PointsToScreenPixelsY((int)(range.Top * 72 / xlApp.InchesToPoints(1)));
                    rangeRect.Bottom = pane.PointsToScreenPixelsY(
                        (int)((range.Top + range.Height) * 72 / xlApp.InchesToPoints(1)));
                    rangeRect.Left = pane.PointsToScreenPixelsX((int)(range.Left * 72 / xlApp.InchesToPoints(1)));
                    rangeRect.Right = pane.PointsToScreenPixelsX(
                        (int)((range.Left + range.Width) * 72 / xlApp.InchesToPoints(1)));
                    break;
                }
            }

            // 如果选区不在任何面板的可视范围内，则返回空的RECT
            if (!isRangeVisible)
            {
                return new WinAPI.RECT();
            }

            return rangeRect;
        }

        /// <summary>
        /// 将Excel的点数(Point)转换为屏幕像素
        /// </summary>
        /// <param name="points">Excel的点数</param>
        /// <returns>对应的屏幕像素值</returns>
        public static int PointsToScreenPixels(double points)
        {
            // 实际像素应考虑屏幕的DPI（每英寸点数）
            using (Graphics graphics = Graphics.FromHwnd(IntPtr.Zero))
            {
                float currentDpiY = graphics.DpiY;
                return Convert.ToInt32(points / 72 * currentDpiY);
            }
        }

        #endregion 获得单元格坐标

        #region Excel窗体操作

        /// <summary>
        /// 激活原窗口Hwnd
        /// </summary>
        public static void ActiveExcelAppHwnd()
        {
            ETFormNative.SetForegroundWindow(ActiveExcelIntPtr);
            PreviousExcelIntPtr = ActiveExcelIntPtr;
        }

        /// <summary>
        /// 记录原窗口Hwnd
        /// </summary>
        public static void SaveExcelAppHwnd()
        {
            ActiveExcelIntPtr = ETFormNative.GetActiveWindow();
        }

        /// <summary>
        /// 判断给定窗口句柄是否属于 Excel 窗口
        /// </summary>
        /// <param name="intPtr">要判断的窗口句柄</param>
        /// <returns>如果窗口句柄属于 Excel 进程，则返回 true；否则返回 false</returns>
        public static bool IsExcelWindow(IntPtr intPtr)
        {
            IntPtr foregroundWindowHandle = ETFormNative.GetForegroundWindow();
            int foregroundProcId;
            int excelProcId;

            ETFormNative.GetWindowThreadProcessId(foregroundWindowHandle, out foregroundProcId);
            ETFormNative.GetWindowThreadProcessId(new IntPtr(XlApp.Hwnd), out excelProcId);

            return foregroundProcId == excelProcId;
        }

        #endregion Excel窗体操作

        #region 从控件取值

        /// <summary>
        /// 将指定列表框的选中项转换为用分隔符连接的字符串。
        /// </summary>
        /// <param name="listBox">要处理的列表框对象</param>
        /// <param name="splitChar">用于连接各选中项的分隔符，默认为分号</param>
        /// <returns>拼接后的字符串，若无选中项则返回null</returns>
        public static string ListBoxSelectedItemToString(ListBox listBox, string splitChar = ";")
        {
            if (listBox?.SelectedItems == null || listBox.SelectedItems.Count == 0)
                return null;

            var items = listBox.SelectedItems.Cast<object>()
                .Where(item => item != null)
                .Select(item => item.ToString())
                .Where(str => !string.IsNullOrEmpty(str))
                .ToList();

            return items.Count > 0 ? string.Join(splitChar, items) : null;
        }

        /// <summary>
        /// 获取指定列表框中的所有选中项，并转换为字符串列表。
        /// </summary>
        /// <param name="listBox">要获取选中项的列表框对象</param>
        /// <returns>包含所有选中项的字符串列表，若无选中项则返回null</returns>
        public static List<string> ListBoxSelectedItem(ListBox listBox)
        {
            return listBox
                ?.SelectedItems.Cast<object>()
                .Select(item => item.ToString())
                .Where(item => !string.IsNullOrEmpty(item))
                .ToList();
        }

        #endregion 从控件取值

        #region 填充控件

        ///// <summary>
        ///// 填充工作簿名称到ComboBox
        ///// </summary>
        ///// <param name="comboBox">ComboBox控件</param>
        ///// <param name="addCurrentWorksheetChar">是否添加当前工作表的字符</param>
        //public static void FillWorkbookNamesToComboBox(ComboBox comboBox, bool addCurrentWorksheetChar = false)
        //{
        //    comboBox.Items.Clear();
        //    if (addCurrentWorksheetChar)
        //        comboBox.Items.Add("[当前工作簿]");
        //    foreach (Workbook wb in XlApp.Workbooks)
        //        comboBox.Items.Add(wb.Name);
        //}

        /// <summary> 填充字符串集合到ComboBox </summary> <param name="comboBox">ComboBox控件</param> <param name="items">字符串集合（支持List<string>、HashSet<string>等）</param>
        public static void LoadComboBox(ComboBox comboBox, IEnumerable<string> items)
        {
            if (comboBox == null || items == null)
                return;

            comboBox.Items.Clear();
            foreach (string item in items)
            {
                if (!string.IsNullOrEmpty(item))
                    comboBox.Items.Add(item);
            }
        }

        /// <summary>
        /// 从指定路径加载符合匹配模式的文件到ComboBox
        /// </summary>
        /// <param name="comboBox">ComboBox控件</param>
        /// <param name="path">要搜索的路径</param>
        /// <param name="searchPattern">文件匹配模式，如"*.txt"</param>
        public static void LoadComboBox(ComboBox comboBox, string path, string searchPattern = "*")
        {
            if (comboBox == null || string.IsNullOrEmpty(path) || string.IsNullOrEmpty(searchPattern))
                return;

            try
            {
                // 获取符合匹配模式的文件列表
                List<string> files = Directory.GetFiles(path, searchPattern)
                                   .Select(Path.GetFileName)
                                   .Where(f => !string.IsNullOrEmpty(f))
                                   .ToList();

                // 调用已有的LoadComboBox方法加载文件列表
                LoadComboBox(comboBox, files);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
            }
        }

        /// <summary>
        /// 填充字符串列表到ListBox
        /// </summary>
        /// <param name="listBox">ListBox控件</param>
        /// <param name="listString">字符串列表</param>
        /// <param name="maxCount">最大显示数量</param>
        /// <param name="clearFirst">是否清空列表</param>
        /// <param name="nullValue">空值显示的字符串</param>
        /// <returns>是否成功填充</returns>
        public static bool LoadListBox(
            ListBox listBox,
            IEnumerable<string> listString,
            int maxCount = 0,
            bool clearFirst = false,
            string nullValue = "--------------------")
        {
            if (listBox == null || listString == null)
                return false;

            if (clearFirst)
                listBox.Items.Clear();

            IEnumerable<string> itemsToAdd = listString
                .Select(str => string.IsNullOrEmpty(str) ? nullValue : str)
                .Take(maxCount > 0 ? maxCount : int.MaxValue);

            listBox.Items.AddRange(itemsToAdd.ToArray());

            return true;
        }

        /// <summary>
        /// 从指定路径加载符合匹配模式的文件到ListBox
        /// </summary>
        /// <param name="listBox">ListBox控件</param>
        /// <param name="path">要搜索的路径</param>
        /// <param name="searchPattern">文件匹配模式，如"*.txt"</param>
        /// <param name="maxCount">最大显示数量</param>
        /// <param name="clearFirst">是否清空列表</param>
        /// <param name="nullValue">空值显示的字符串</param>
        /// <returns>是否成功填充</returns>
        public static bool LoadListBox(
            ListBox listBox,
            string path,
            string searchPattern = "*",
            int maxCount = 0,
            bool clearFirst = false,
            string nullValue = "--------------------")
        {
            if (listBox == null || string.IsNullOrEmpty(path))
                return false;

            try
            {
                // 获取符合匹配模式的文件列表
                List<string> files = Directory.GetFiles(path, searchPattern)
                                   .Select(Path.GetFileName)
                                   .Where(f => !string.IsNullOrEmpty(f))
                                   .ToList();

                // 调用已有的LoadListBox方法加载文件列表
                return LoadListBox(listBox, files, maxCount, clearFirst, nullValue);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                return false;
            }
        }

        /// <summary>
        /// 填充字符串列表到CheckedListBox
        /// </summary>
        /// <param name="checkedListBox">CheckedListBox控件</param>
        /// <param name="listString">字符串列表</param>
        /// <param name="maxCount">最大显示数量</param>
        /// <param name="clearFirst">是否清空列表</param>
        /// <param name="nullValue">空值显示的字符串</param>
        /// <returns>是否成功填充</returns>
        public static bool LoadCheckedListBox(
            CheckedListBox checkedListBox,
            IEnumerable<string> listString,
            int maxCount = 0,
            bool clearFirst = false,
            string nullValue = "--------------------")
        {
            if (checkedListBox == null || listString == null)
                return false;

            if (clearFirst)
                checkedListBox.Items.Clear();

            IEnumerable<string> itemsToAdd = listString
                .Select(str => str ?? nullValue)
                .Take(maxCount > 0 ? maxCount : int.MaxValue);

            checkedListBox.Items.AddRange(itemsToAdd.ToArray());

            CheckedListBoxItemInvalid(checkedListBox, "--------");

            return true;
        }

        /// <summary>
        /// 从指定路径加载符合匹配模式的文件到CheckedListBox
        /// </summary>
        /// <param name="checkedListBox">CheckedListBox控件</param>
        /// <param name="path">要搜索的路径</param>
        /// <param name="searchPattern">文件匹配模式，如"*.txt"</param>
        /// <param name="maxCount">最大显示数量</param>
        /// <param name="clearFirst">是否清空列表</param>
        /// <param name="nullValue">空值显示的字符串</param>
        /// <returns>是否成功填充</returns>
        public static bool LoadCheckedListBox(
            CheckedListBox checkedListBox,
            string path,
            string searchPattern = "*",
            int maxCount = 0,
            bool clearFirst = false,
            string nullValue = "--------------------")
        {
            if (checkedListBox == null || string.IsNullOrEmpty(path))
                return false;

            try
            {
                // 获取符合匹配模式的文件列表
                List<string> files = Directory.GetFiles(path, searchPattern)
                                   .Select(Path.GetFileName)
                                   .Where(f => !string.IsNullOrEmpty(f))
                                   .ToList();

                // 调用已有的LoadCheckedListBox方法加载文件列表
                return LoadCheckedListBox(checkedListBox, files, maxCount, clearFirst, nullValue);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                return false;
            }
        }

        /// <summary>
        /// 加载上下文菜单
        /// </summary>
        /// <param name="contextMenuStrip">上下文菜单控件</param>
        /// <param name="items">字典，键为菜单项名称，值为对应的字符串数组</param>
        /// <param name="textBox">文本框控件</param>
        /// <remarks>点击ContextMenuStrip中的菜单项后，将对应的字符串数组内容显示到文本框中，每个字符串占一行。</remarks>
        public static void LoadContextMenuStrip(
            ContextMenuStrip contextMenuStrip,
            Dictionary<string, string[]> items,
            TextBox textBox)
        {
            // 先清空contextMenuStrip下原有菜单
            contextMenuStrip.Items.Clear();

            // 遍历字典中的每一个元素
            foreach (KeyValuePair<string, string[]> keyValuePair in items)
            {
                ToolStripMenuItem menuItem = new(keyValuePair.Key);
                menuItem.Click += (sender, e) => textBox.Text = string.Join(Environment.NewLine, keyValuePair.Value);

                contextMenuStrip.Items.Add(menuItem);
            }
        }

        /// <summary>
        /// 直接从配置文件加载ContextMenuStrip菜单，并在菜单底部添加管理功能
        /// </summary>
        /// <param name="contextMenuStrip">要加载的上下文菜单控件</param>
        /// <param name="configFileName">配置文件名（不含路径）</param>
        /// <param name="textBox">关联的文本框控件</param>
        /// <remarks>
        /// 该方法会：
        /// 1. 从指定配置文件加载菜单项
        /// 2. 在菜单底部添加分隔符
        /// 3. 添加"打开配置文件"和"刷新菜单"两个管理功能按钮
        /// 4. 为管理功能按钮绑定相应的事件处理
        /// </remarks>
        public static void LoadContextMenuStripFromConfig(ContextMenuStrip contextMenuStrip, string configFileName, TextBox textBox)
        {
            try
            {
                // 先清空contextMenuStrip下原有菜单
                contextMenuStrip.Items.Clear();

                // 从配置文件加载字符规整预置规则字典
                Dictionary<string, string[]> contextMenuDictionary = ETConfig.ConfigFileToDictionary(ETConfig.GetConfigDirectory(configFileName));

                // 遍历字典中的每一个元素，添加配置项菜单
                foreach (KeyValuePair<string, string[]> keyValuePair in contextMenuDictionary)
                {
                    ToolStripMenuItem menuItem = new ToolStripMenuItem(keyValuePair.Key);
                    menuItem.Click += (sender, e) => textBox.Text = string.Join(Environment.NewLine, keyValuePair.Value);
                    contextMenuStrip.Items.Add(menuItem);
                }

                // 如果有菜单项，添加分隔符
                if (contextMenuStrip.Items.Count > 0)
                {
                    contextMenuStrip.Items.Add(new ToolStripSeparator());
                }

                // 添加"打开配置文件"菜单项
                ToolStripMenuItem openConfigItem = new ToolStripMenuItem("打开配置文件");
                openConfigItem.Click += (sender, e) =>
                {
                    try
                    {
                        ETLogManager.Info(typeof(ETForm), $"用户点击打开配置文件：{configFileName}");
                        ETConfig.OpenConfigFile(configFileName);
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error(typeof(ETForm), $"打开配置文件 {configFileName} 时发生错误", ex);
                        MessageBox.Show($"打开配置文件失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                };
                contextMenuStrip.Items.Add(openConfigItem);

                // 添加"刷新菜单"菜单项
                ToolStripMenuItem refreshMenuItem = new ToolStripMenuItem("刷新菜单");
                refreshMenuItem.Click += (sender, e) =>
                {
                    try
                    {
                        ETLogManager.Info(typeof(ETForm), $"用户点击刷新菜单：{configFileName}");
                        LoadContextMenuStripFromConfig(contextMenuStrip, configFileName, textBox);
                        //MessageBox.Show("菜单已刷新", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error(typeof(ETForm), $"刷新菜单 {configFileName} 时发生错误", ex);
                        MessageBox.Show($"刷新菜单失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                };
                contextMenuStrip.Items.Add(refreshMenuItem);

                ETLogManager.Info(typeof(ETForm), $"成功加载配置文件菜单：{configFileName}，共 {contextMenuDictionary.Count} 个配置项");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(typeof(ETForm), $"加载配置文件菜单 {configFileName} 时发生错误", ex);
                MessageBox.Show($"加载配置文件菜单失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// RibbonGallery配置信息存储字典，用于在点击事件中获取配置信息
        /// </summary>
        private static readonly Dictionary<object, RibbonGalleryConfigInfo> _ribbonGalleryConfigs = new Dictionary<object, RibbonGalleryConfigInfo>();

        /// <summary>
        /// RibbonGallery配置信息类
        /// </summary>
        private class RibbonGalleryConfigInfo
        {
            public string ConfigFileName { get; set; }
            public System.Action RefreshCallback { get; set; }
            public object Factory { get; set; }
        }

        /// <summary>
        /// 从配置文件加载RibbonGallery菜单，并在菜单底部添加管理功能
        /// </summary>
        /// <param name="gallery">要加载的RibbonGallery控件</param>
        /// <param name="configFileName">配置文件名（不含路径）</param>
        /// <param name="factory">Ribbon工厂对象，用于创建Ribbon控件</param>
        /// <param name="refreshCallback">刷新列表时的回调方法（可选）</param>
        /// <remarks>
        /// 该方法会：
        /// 1. 从指定配置文件加载菜单项（格式：显示名称=文件路径）
        /// 2. 在菜单底部添加分隔符（使用RibbonDropDownItem模拟）
        /// 3. 添加"打开配置文件"和"刷新菜单"两个管理功能按钮
        /// 4. 自动处理管理功能按钮的点击事件，外部无需编写相关代码
        /// 5. 外部只需要调用 HandleRibbonGalleryItemClick 处理用户列表项点击
        /// </remarks>
        public static void LoadRibbonGalleryFromConfig(object gallery, string configFileName, object factory, System.Action refreshCallback = null)
        {
            try
            {
                // 存储配置信息，供点击事件使用
                _ribbonGalleryConfigs[gallery] = new RibbonGalleryConfigInfo
                {
                    ConfigFileName = configFileName,
                    RefreshCallback = refreshCallback,
                    Factory = factory
                };

                // 直接转换为具体类型，避免反射问题
                if (gallery is Microsoft.Office.Tools.Ribbon.RibbonGallery ribbonGallery &&
                    factory is Microsoft.Office.Tools.Ribbon.RibbonFactory ribbonFactory)
                {
                    // 直接使用强类型操作
                    ribbonGallery.Items.Clear();

                    // 从配置文件加载菜单项
                    string configPath = ETConfig.GetConfigDirectory(configFileName);

                    // 如果配置文件不存在，创建默认配置文件
                    if (!File.Exists(configPath))
                    {
                        CreateDefaultConfigFile(configPath, configFileName);
                    }

                    // 读取配置文件内容
                    if (File.Exists(configPath))
                    {
                        string[] lines = File.ReadAllLines(configPath);
                        foreach (string line in lines)
                        {
                            if (string.IsNullOrWhiteSpace(line) || line.StartsWith("#"))
                                continue;

                            string[] parts = line.Split('=');
                            if (parts.Length == 2)
                            {
                                string displayName = parts[0].Trim();
                                string filePath = parts[1].Trim();

                                var dropDownItem = ribbonFactory.CreateRibbonDropDownItem();
                                dropDownItem.Label = displayName;
                                dropDownItem.Tag = filePath;
                                ribbonGallery.Items.Add(dropDownItem);
                            }
                        }
                    }

                    // 如果有菜单项，添加分隔符
                    if (ribbonGallery.Items.Count > 0)
                    {
                        var separator = ribbonFactory.CreateRibbonDropDownItem();
                        separator.Label = "──────────────";
                        separator.Tag = "SEPARATOR";
                        ribbonGallery.Items.Add(separator);
                    }

                    // 添加"打开配置文件"按钮
                    var openConfigItem = ribbonFactory.CreateRibbonDropDownItem();
                    openConfigItem.Label = "打开配置文件";
                    openConfigItem.Tag = "OPEN_CONFIG";
                    ribbonGallery.Items.Add(openConfigItem);

                    // 添加"刷新文件列表"按钮
                    var refreshItem = ribbonFactory.CreateRibbonDropDownItem();
                    refreshItem.Label = "刷新文件列表";
                    refreshItem.Tag = "REFRESH_LIST";
                    ribbonGallery.Items.Add(refreshItem);

                    int itemCount = ribbonGallery.Items.Count - 2; // 减去管理按钮数量
                    ETLogManager.Info(typeof(ETForm), $"成功加载RibbonGallery配置文件菜单：{configFileName}，共 {itemCount} 个配置项");
                }
                else
                {
                    ETLogManager.Error(typeof(ETForm), $"Gallery类型不匹配，期望RibbonGallery，实际：{gallery?.GetType().FullName}，Factory类型：{factory?.GetType().FullName}");
                    return;
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(typeof(ETForm), $"加载RibbonGallery配置文件菜单 {configFileName} 时发生错误", ex);
                throw new ETException($"加载RibbonGallery配置文件菜单失败：{ex.Message}", "配置操作", ex);
            }
        }

        /// <summary>
        /// 通用的RibbonGallery文件项点击处理方法
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="excelApplication">Excel应用程序实例（可选）</param>
        /// <returns>是否成功处理</returns>
        /// <remarks>
        /// 此方法提供了标准的文件打开逻辑：
        /// 1. 文件夹 - 用资源管理器打开
        /// 2. Excel文件 - 在Excel中打开（如果提供了Excel实例）
        /// 3. 其他文件 - 用默认程序打开
        /// </remarks>
        public static bool HandleRibbonGalleryFileClick(string filePath, object excelApplication = null)
        {
            try
            {
                return ETFile.OpenFileOrDirectory(filePath, excelApplication);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(typeof(ETForm), $"处理文件点击事件时发生错误", ex);
                MessageBox.Show($"打开文件失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// 完整的RibbonGallery点击处理方法（包含管理功能和文件处理）
        /// </summary>
        /// <param name="gallery">RibbonGallery控件</param>
        /// <param name="tagValue">点击项的Tag值</param>
        /// <param name="excelApplication">Excel应用程序实例（可选）</param>
        /// <returns>是否成功处理</returns>
        /// <remarks>
        /// 此方法是最简化的调用方式，自动处理所有类型的点击：
        /// 1. 管理功能按钮 - 自动处理
        /// 2. 分隔符 - 自动忽略
        /// 3. 文件项 - 使用通用文件打开逻辑
        /// </remarks>
        public static bool HandleRibbonGalleryClickComplete(object gallery, string tagValue, object excelApplication = null)
        {
            return HandleRibbonGalleryItemClick(gallery, tagValue, filePath => HandleRibbonGalleryFileClick(filePath, excelApplication));
        }

        /// <summary>
        /// 创建默认配置文件
        /// </summary>
        /// <param name="configPath">配置文件完整路径</param>
        /// <param name="configFileName">配置文件名</param>
        /// <remarks>
        /// 根据不同的配置文件名创建相应的默认内容：
        /// - 收藏文件.config: 创建文件和文件夹的示例
        /// - 其他配置文件: 创建通用示例
        /// </remarks>
        private static void CreateDefaultConfigFile(string configPath, string configFileName)
        {
            try
            {
                string defaultContent;

                // 根据配置文件名创建不同的默认内容
                if (configFileName.Contains("收藏文件") || configFileName.Contains("常用文件"))
                {
                    // 为常用文件配置创建示例
                    defaultContent = @"# 常用文件配置文件
# 格式：显示名称=文件路径
# 支持文件和文件夹路径
# 以 # 开头的行为注释

示例Excel文件=C:\Users\<USER>\Documents\示例.xlsx
示例文档文件夹=C:\Users\<USER>\Documents";
                }
                else
                {
                    // 为其他配置文件创建通用示例
                    defaultContent = @"# 配置文件
# 格式：显示名称=文件路径
# 以 # 开头的行为注释

示例项目1=C:\示例路径1
示例项目2=C:\示例路径2";
                }

                // 确保目录存在
                string directory = Path.GetDirectoryName(configPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // 写入默认配置文件
                File.WriteAllText(configPath, defaultContent, System.Text.Encoding.UTF8);

                ETLogManager.Info(typeof(ETForm), $"已创建默认配置文件：{configPath}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error(typeof(ETForm), $"创建默认配置文件失败：{configPath}", ex);
            }
        }

        /// <summary>
        /// 处理RibbonGallery配置菜单的点击事件（通用处理方法）
        /// </summary>
        /// <param name="gallery">RibbonGallery控件</param>
        /// <param name="tagValue">点击项的Tag值</param>
        /// <param name="itemClickCallback">用户列表项点击回调方法</param>
        /// <returns>如果是管理功能按钮返回true，如果是用户列表项返回false</returns>
        /// <remarks>
        /// 该方法会自动处理以下情况：
        /// 1. "OPEN_CONFIG" - 自动打开配置文件（无需外部代码）
        /// 2. "REFRESH_LIST" - 自动刷新列表（无需外部代码）
        /// 3. "SEPARATOR" - 忽略分隔符点击
        /// 4. 其他值 - 调用itemClickCallback处理用户列表项 注意：使用此方法前必须先调用 LoadRibbonGalleryFromConfig 方法
        /// </remarks>
        public static bool HandleRibbonGalleryItemClick(object gallery, string tagValue, System.Action<string> itemClickCallback)
        {
            try
            {
                if (string.IsNullOrEmpty(tagValue))
                    return true;

                // 获取存储的配置信息
                if (!_ribbonGalleryConfigs.TryGetValue(gallery, out RibbonGalleryConfigInfo configInfo))
                {
                    ETLogManager.Error(typeof(ETForm), "未找到RibbonGallery的配置信息，请先调用LoadRibbonGalleryFromConfig方法");
                    return true;
                }

                // 处理管理功能按钮
                if (tagValue == "OPEN_CONFIG")
                {
                    try
                    {
                        ETLogManager.Info(typeof(ETForm), $"用户点击打开配置文件：{configInfo.ConfigFileName}");
                        ETConfig.OpenConfigFile(configInfo.ConfigFileName);
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error(typeof(ETForm), $"打开配置文件 {configInfo.ConfigFileName} 时发生错误", ex);
                        MessageBox.Show($"打开配置文件失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                    return true;
                }

                if (tagValue == "REFRESH_LIST")
                {
                    try
                    {
                        ETLogManager.Info(typeof(ETForm), $"用户点击刷新菜单：{configInfo.ConfigFileName}");
                        configInfo.RefreshCallback?.Invoke();
                        MessageBox.Show("文件列表已刷新", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error(typeof(ETForm), $"刷新菜单 {configInfo.ConfigFileName} 时发生错误", ex);
                        MessageBox.Show($"刷新菜单失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                    return true;
                }

                // 忽略分隔符点击
                if (tagValue == "SEPARATOR")
                {
                    return true;
                }

                // 处理用户列表项
                itemClickCallback?.Invoke(tagValue);
                return false;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(typeof(ETForm), $"处理RibbonGallery点击事件时发生错误", ex);
                MessageBox.Show($"处理点击事件失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return true;
            }
        }

        /// <summary>
        /// 设置特定前缀的为失效项
        /// </summary>
        /// <param name="listBox">CheckedListBox控件</param>
        /// <param name="startsWithPrefix">前缀字符串</param>
        public static void CheckedListBoxItemInvalid(CheckedListBox listBox, string startsWithPrefix)
        {
            //可能有Bug
            for (int i = 0; i < listBox.Items.Count; i++)
                if (listBox.Items[i].ToString().StartsWith(startsWithPrefix))
                {
                    listBox.SetItemCheckState(i, CheckState.Indeterminate);
                    listBox.SetItemChecked(i, false);
                }
        }

        /// <summary>
        /// 往ListView中添加提醒信息
        /// </summary>
        /// <param name="listView">ListView控件</param>
        /// <param name="message">提醒信息</param>
        /// <param name="clear">是否清空列表</param>
        public static void AddMessageToListview(ListView listView, string message, bool clear = true)
        {
            if (clear)
                listView.Items.Clear();
            ListViewItem lvi = new();
            if (string.IsNullOrEmpty(message))
                return;
            lvi.Text = message;
            listView.Items.Add(lvi);
        }

        // 隐藏指定的TabPage
        public static void HideTabPage(this TabControl tabControl, TabPage tabPage)
        {
            if (tabControl == null || tabPage == null)
                return;
            if (tabControl.TabPages.Contains(tabPage))
            {
                tabControl.TabPages.Remove(tabPage);
            }
        }

        // 显示指定的TabPage
        public static void ShowTabPage(this TabControl tabControl, TabPage tabPage)
        {
            if (tabControl == null || tabPage == null)
                return;
            // 通过判断是否已包含该页来防止重复添加
            if (!tabControl.TabPages.Contains(tabPage))
            {
                tabControl.TabPages.Add(tabPage);
            }
        }

        #endregion 填充控件

        #region 控件属性设置

        /// <summary>
        /// 跨线程设置控件的Enabled属性
        /// </summary>
        /// <param name="control">要设置的控件</param>
        /// <param name="enabled">是否启用</param>
        public static void EnabledInvoke(this Control control, bool enabled)
        {
            if (control == null)
                return;

            try
            {
                control?.Invoke(
                    new EventHandler(
                        delegate
                        {
                            control.Enabled = enabled;
                        }));
            }
            catch (Exception e)
            {
                Debug.Print(e.Message);
            }
        }

        /// <summary>
        /// 跨线程设置控件的Visible属性
        /// </summary>
        /// <param name="control">要设置的控件</param>
        /// <param name="visible">是否可见</param>
        public static void VisibleInvoke(this Control control, bool visible)
        {
            if (control == null)
                return;

            try
            {
                control?.Invoke(
                    new EventHandler(
                        delegate
                        {
                            control.Visible = visible;
                        }));
            }
            catch (Exception e)
            {
                Debug.Print(e.Message);
            }
        }

        /// <summary>
        /// 更改按键显示文字
        /// </summary>
        /// <param name="button">Button控件</param>
        /// <param name="text">显示文字</param>
        public static void TextInvoke(this Button button, string text)
        {
            button.Invoke(
                new EventHandler(
                    delegate
                    {
                        button.Text = text;
                    }));
        }

        /// <summary>
        /// 跨线程清空TextBox的内容
        /// </summary>
        /// <param name="textBox">要清空的TextBox控件</param>
        public static void ClearInvoke(this TextBox textBox)
        {
            if (textBox == null)
                return;

            try
            {
                textBox?.Invoke(
                    new EventHandler(
                        delegate
                        {
                            textBox.Clear();
                        }));
            }
            catch (Exception e)
            {
                Debug.Print(e.Message);
            }
        }

        #endregion 控件属性设置

        #region 操作配置文件

        /// <summary>
        /// 绑定控件的值到INI配置文件的指定节和键，同时监听控件变更事件以自动保存回INI文件。
        /// </summary>
        /// <param name="control">动态控件对象，支持多种类型如CheckBox、TextBox、DateTimePicker等。</param>
        /// <param name="iniFile">指向INI文件的实例，用于读取和写入配置。</param>
        /// <param name="section">INI文件中的节名称。</param>
        /// <param name="key">INI文件中用于存储控件值的键名称。</param>
        /// <remarks>
        /// 本方法首先尝试从INI文件指定节和键读取值，初始化控件状态： - 对于复选框类型，根据"1"或"0"设置选中状态。 -
        /// 对于DateTimePicker类型，尝试将INI中的值解析为日期时间。 - 对于其他类型，默认设置文本内容。 随后，为不同类型的控件注册事件处理器，当控件值发生变化时： -
        /// 文本框、组合框更改文本时，自动保存新值到INI文件。 - 复选框状态改变时，更新INI文件中的值为"1"或"0"。 - 自定义文件选择控件路径变化时，同步更新INI文件记录。
        /// - DateTimePicker值变化时，将日期时间保存到INI文件。 异常情况被捕获并通过日志管理器记录错误。
        /// </remarks>
        public static void BindWindowsFormControl(
            dynamic control,
            ETIniFile iniFile,
            string section,
            string key,
            bool isComboBoxHistory = false)
        {
            if (control == null || iniFile == null || string.IsNullOrEmpty(section) || string.IsNullOrEmpty(key))
                return;

            // 初始化控件的值
            try
            {
                string value = iniFile.IniReadValue(section, key);

                // 如果是ComboBox且启用历史记录，则需要特殊处理
                if (control is ComboBox comboBoxInit && isComboBoxHistory)
                {
                    string optionsString = value;
                    if (!string.IsNullOrEmpty(optionsString))
                    {
                        comboBoxInit.Items.Clear();
                        comboBoxInit.Items.AddRange(optionsString.Split(OptionsSeparator));
                    }
                }
                else
                {
                    switch (control)
                    {
                        case RibbonCheckBox ribbonCheckBox:
                            ribbonCheckBox.Checked = value.Trim() == "1" || value.Trim().Equals("true", StringComparison.OrdinalIgnoreCase);
                            break;

                        case CheckBox checkBox:
                            checkBox.Checked = value.Trim() == "1" || value.Trim().Equals("true", StringComparison.OrdinalIgnoreCase);
                            break;

                        case RadioButton radioButton:
                            radioButton.Checked = value.Trim() == "1" || value.Trim().Equals("true", StringComparison.OrdinalIgnoreCase);
                            break;

                        case TextBox textBox:
                            textBox.Text = value.Replace("\\r", Environment.NewLine);
                            break;

                        case ListBox listBox:
                            if (!string.IsNullOrEmpty(value))
                            {
                                List<int> selectedIndices = value.Split(',').Select(int.Parse).ToList();
                                foreach (int index in selectedIndices)
                                {
                                    if (index >= 0 && index < listBox.Items.Count)
                                        listBox.SetSelected(index, true);
                                }
                            }
                            break;

                        case NumericUpDown numericUpDown:
                            if (decimal.TryParse(value, out decimal numValue))
                            {
                                numericUpDown.Value = Math.Max(numericUpDown.Minimum,
                                    Math.Min(numericUpDown.Maximum, numValue));
                            }
                            break;

                        case DateTimePicker dateTimePicker:
                            if (DateTime.TryParse(value, out DateTime dateTimeValue))
                            {
                                dateTimePicker.Value = dateTimeValue;
                            }
                            break;

                        case ComboBox comboBox:
                            comboBox.Text = value;
                            break;

                        default:
                            if (control is Control ctrl)
                                ctrl.Text = value;
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
            }

            // 添加事件处理程序以捕捉变更
            try
            {
                // 如果是ComboBox且启用历史记录，则需要特殊处理
                if (control is ComboBox comboBoxEvents && isComboBoxHistory)
                {
                    bool eventsEnabled = true;

                    void UpdateComboBoxAndSave()
                    {
                        if (!eventsEnabled) return;
                        try
                        {
                            eventsEnabled = false;
                            List<string> currentItems = new(comboBoxEvents.Items.Cast<string>());
                            string currentText = comboBoxEvents.Text.Trim();

                            if (!string.IsNullOrEmpty(currentText))
                            {
                                currentItems.Remove(currentText);
                                currentItems.Insert(0, currentText);
                            }

                            if (currentItems.Count > MaxComboBoxItems)
                            {
                                currentItems = currentItems.Take(MaxComboBoxItems).ToList();
                            }

                            string optionsString = string.Join(OptionsSeparator.ToString(), currentItems);
                            iniFile.IniWriteValue(section, key, optionsString);
                            iniFile.IniWriteFile();

                            comboBoxEvents.Items.Clear();
                            comboBoxEvents.Items.AddRange(currentItems.ToArray());
                            comboBoxEvents.Text = currentText;
                        }
                        finally
                        {
                            eventsEnabled = true;
                        }
                    }

                    // 安全添加事件处理器，避免重复绑定
                    comboBoxEvents.Leave -= (sender, e) => UpdateComboBoxAndSave();
                    comboBoxEvents.Leave += (sender, e) => UpdateComboBoxAndSave();
                }
                else
                {
                    switch (control)
                    {
                        case RibbonCheckBox ribbonCheckBox:
                            ribbonCheckBox.Click += (sender, e) =>
                            {
                                iniFile.IniWriteValue(section, key, ribbonCheckBox.Checked ? "1" : "0");
                                iniFile.IniWriteFile();
                            };
                            break;

                        case TextBox textBox:
                            textBox.TextChanged += (sender, e) =>
                            {
                                iniFile.IniWriteValue(section, key, textBox.Text.Replace(Environment.NewLine, "\\r"));
                                iniFile.IniWriteFile();
                            };
                            break;

                        case CheckBox checkBox:
                            checkBox.CheckedChanged += (sender, e) =>
                            {
                                iniFile.IniWriteValue(section, key, checkBox.Checked ? "1" : "0");
                                iniFile.IniWriteFile();
                            };
                            break;

                        case RadioButton radioButton:
                            radioButton.CheckedChanged += (sender, e) =>
                            {
                                if (radioButton.Checked) // 只在选中时保存，避免重复
                                {
                                    iniFile.IniWriteValue(section, key, "1");
                                    iniFile.IniWriteFile();
                                }
                            };
                            break;

                        case ListBox listBox:
                            listBox.SelectedIndexChanged += (sender, e) =>
                            {
                                List<int> selectedIndices = listBox.SelectedIndices.Cast<int>().ToList();
                                string indices = string.Join(",", selectedIndices);
                                iniFile.IniWriteValue(section, key, indices);
                                iniFile.IniWriteFile();
                            };
                            break;

                        case NumericUpDown numericUpDown:
                            numericUpDown.ValueChanged += (sender, e) =>
                            {
                                iniFile.IniWriteValue(section, key, numericUpDown.Value.ToString());
                                iniFile.IniWriteFile();
                            };
                            break;

                        case ComboBox comboBox:
                            comboBox.TextChanged += (sender, e) =>
                            {
                                iniFile.IniWriteValue(section, key, comboBox.Text);
                                iniFile.IniWriteFile();
                            };
                            comboBox.SelectedIndexChanged += (sender, e) =>
                            {
                                if (comboBox.SelectedItem != null)
                                {
                                    iniFile.IniWriteValue(section, key, comboBox.SelectedItem.ToString());
                                    iniFile.IniWriteFile();
                                }
                            };
                            break;

                        case DateTimePicker dateTimePicker:
                            dateTimePicker.ValueChanged += (sender, e) =>
                            {
                                iniFile.IniWriteValue(section, key, dateTimePicker.Value.ToString("yyyy-MM-dd HH:mm:ss"));
                                iniFile.IniWriteFile();
                            };
                            break;

                        default:
                            if (control is Control ctrl)
                            {
                                ctrl.TextChanged += (sender, e) =>
                                {
                                    iniFile.IniWriteValue(section, key, ctrl.Text);
                                    iniFile.IniWriteFile();
                                };
                            }
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
            }
        }

        /// <summary>
        /// 绑定ComboBox控件和历史记录管理，自动生成historyKey（推荐使用）
        /// </summary>
        /// <param name="comboBox">要绑定的ComboBox控件</param>
        /// <param name="maxHistoryCount">最大历史记录数量，默认30</param>
        /// <param name="autoFillLatestValue">是否自动填充最近的值，默认true</param>
        /// <param name="defaultValuesFilePath">下拉列表默认值文件路径，一行为一个值。如果为null或空，则不使用默认值功能</param>
        /// <remarks>
        /// 此方法会：
        /// 1. 自动根据窗体类名和控件名生成唯一的historyKey
        /// 2. 自动加载历史记录到ComboBox
        /// 3. 如果指定了默认值文件，会加载默认值到下拉列表
        /// 4. 历史值和默认值如果重复，只会出现一次
        /// 5. 下拉列表中，历史值在前面，默认值在后面
        /// 6. 监听用户输入变化并自动保存到历史记录文件
        /// 7. 历史记录保存在 Data\ET\Cache\ETForm\ComboBoxHistory\ 目录下
        /// 8. 文件名格式：{FormClassName}_{ControlName}.data
        /// 9. 最新的记录会自动排在最前面
        /// </remarks>
        public static void BindComboBox(ComboBox comboBox, int maxHistoryCount = 30, bool autoFillLatestValue = true, string defaultValuesFilePath = null)
        {
            if (comboBox == null)
                return;

            try
            {
                // 自动生成historyKey
                string historyKey = GenerateComboBoxHistoryKey(comboBox);

                // 调用带historyKey的重载方法
                BindComboBox(comboBox, historyKey, maxHistoryCount, autoFillLatestValue, defaultValuesFilePath);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(typeof(ETForm), "绑定ComboBox历史记录时发生错误（自动生成historyKey）", ex);
            }
        }

        /// <summary>
        /// 绑定ComboBox控件和历史记录管理，参照ETUcFileSelect的历史记录管理功能
        /// </summary>
        /// <param name="comboBox">要绑定的ComboBox控件</param>
        /// <param name="historyKey">历史记录的唯一标识键，用于生成历史记录文件名</param>
        /// <param name="maxHistoryCount">最大历史记录数量，默认30</param>
        /// <param name="autoFillLatestValue">是否自动填充最近的值，默认true</param>
        /// <param name="defaultValuesFilePath">下拉列表默认值文件路径，一行为一个值。如果为null或空，则不使用默认值功能</param>
        /// <remarks>
        /// 此方法会：
        /// 1. 自动加载历史记录到ComboBox
        /// 2. 如果指定了默认值文件，会加载默认值到下拉列表
        /// 3. 历史值和默认值如果重复，只会出现一次
        /// 4. 下拉列表中，历史值在前面，默认值在后面
        /// 5. 监听用户输入变化并自动保存到历史记录文件
        /// 6. 历史记录保存在 Data\ET\Cache\ETForm\ComboBoxHistory\ 目录下
        /// 7. 文件名格式：{historyKey}.data
        /// 8. 最新的记录会自动排在最前面
        /// </remarks>
        public static void BindComboBox(ComboBox comboBox, string historyKey, int maxHistoryCount = 30, bool autoFillLatestValue = true, string defaultValuesFilePath = null)
        {
            if (comboBox == null || string.IsNullOrWhiteSpace(historyKey))
                return;

            try
            {
                // 加载历史记录和默认值
                LoadComboBoxHistory(comboBox, historyKey, autoFillLatestValue, defaultValuesFilePath);

                // 绑定事件处理器
                EventHandler leaveHandler = (sender, e) =>
                {
                    // 检查是否正在更新历史记录，避免递归调用
                    if (comboBox.Tag?.ToString() == "UPDATING_HISTORY")
                        return;

                    SaveComboBoxHistory(comboBox, historyKey, maxHistoryCount);
                };

                EventHandler selectedIndexChangedHandler = (sender, e) =>
                {
                    // 检查是否正在更新历史记录，避免递归调用
                    if (comboBox.Tag?.ToString() == "UPDATING_HISTORY")
                        return;

                    SaveComboBoxHistory(comboBox, historyKey, maxHistoryCount);
                };

                // 安全绑定事件，先移除再添加以避免重复绑定
                comboBox.Leave -= leaveHandler;
                comboBox.Leave += leaveHandler;

                comboBox.SelectedIndexChanged -= selectedIndexChangedHandler;
                comboBox.SelectedIndexChanged += selectedIndexChangedHandler;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(typeof(ETForm), "绑定ComboBox历史记录时发生错误", ex);
            }
        }

        /// <summary>
        /// 绑定ComboBox控件到INI文件中的选项（向后兼容性方法）
        /// </summary>
        /// <param name="comboBox">要绑定的ComboBox控件</param>
        /// <param name="iniFile">INI文件实例</param>
        /// <param name="section">INI文件中的节名称</param>
        /// <param name="key">INI文件中存储选项的键名称</param>
        /// <remarks>此方法为向后兼容性而保留，建议使用新的基于文件的历史记录管理方法。 新方法调用格式：BindComboBox(comboBox, "uniqueHistoryKey")</remarks>
        public static void BindComboBox(ComboBox comboBox, ETIniFile iniFile, string section, string key)
        {
            if (comboBox == null || iniFile == null || string.IsNullOrEmpty(section) || string.IsNullOrEmpty(key))
                return;

            try
            {
                // 从INI文件中获取选项字符串
                string optionsString = iniFile.IniReadValue(section, key);
                if (!string.IsNullOrEmpty(optionsString))
                {
                    // 将选项字符串分割成数组并添加到ComboBox的Items中
                    comboBox.Items.AddRange(optionsString.Split(OptionsSeparator));
                }

                // 创建一个标志来控制是否触发事件
                bool eventsEnabled = true;

                // 创建事件处理器委托
                EventHandler updateHandler = (sender, e) =>
                {
                    if (!eventsEnabled) return;

                    try
                    {
                        eventsEnabled = false;

                        List<string> currentItems = new List<string>(comboBox.Items.Cast<string>());
                        string currentText = comboBox.Text.Trim();

                        if (!string.IsNullOrEmpty(currentText))
                        {
                            currentItems.Remove(currentText);
                            currentItems.Insert(0, currentText);
                        }

                        if (currentItems.Count > MaxComboBoxItems)
                        {
                            currentItems = currentItems.Take(MaxComboBoxItems).ToList();
                        }

                        string updatedOptionsString = string.Join(OptionsSeparator.ToString(), currentItems);
                        iniFile.IniWriteValue(section, key, updatedOptionsString);
                        iniFile.IniWriteFile();

                        comboBox.Items.Clear();
                        comboBox.Items.AddRange(currentItems.ToArray());
                        comboBox.Text = currentText;
                    }
                    finally
                    {
                        eventsEnabled = true;
                    }
                };

                // 安全绑定事件，先移除再添加以避免重复绑定
                comboBox.Leave -= updateHandler;
                comboBox.Leave += updateHandler;

                comboBox.SelectedIndexChanged -= updateHandler;
                comboBox.SelectedIndexChanged += updateHandler;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(typeof(ETForm), "绑定ComboBox到INI文件时发生错误", ex);
            }
        }

        /// <summary>
        /// 自动生成ComboBox历史记录的唯一标识键，参照ETUcFileSelect的实现方式
        /// </summary>
        /// <param name="comboBox">ComboBox控件</param>
        /// <returns>生成的唯一标识键</returns>
        private static string GenerateComboBoxHistoryKey(ComboBox comboBox)
        {
            try
            {
                // 获取当前控件所在窗体的类名
                string formClassName = comboBox.FindForm()?.GetType().Name ?? "Unknown";

                // 获取控件名称，如果为空则使用默认名称
                string controlName = string.IsNullOrWhiteSpace(comboBox.Name) ? "ComboBox" : comboBox.Name;

                // 使用窗体类名和控件名生成唯一标识键
                return $"{formClassName}_{controlName}";
            }
            catch (Exception ex)
            {
                ETLogManager.Error(typeof(ETForm), "生成ComboBox历史记录键时发生错误", ex);
                // 返回一个默认的键名
                return $"Unknown_ComboBox_{DateTime.Now.Ticks}";
            }
        }

        /// <summary>
        /// 获取ComboBox历史记录文件路径
        /// </summary>
        /// <param name="historyKey">历史记录的唯一标识键</param>
        /// <returns>历史记录文件的完整路径</returns>
        private static string GetComboBoxHistoryFilePath(string historyKey)
        {
            string baseDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "ET", "Cache", "ETForm", "ComboBoxHistory");

            // 确保目录存在
            if (!Directory.Exists(baseDir))
            {
                Directory.CreateDirectory(baseDir);
            }

            // 使用historyKey生成文件名
            string fileName = $"{historyKey}.data";

            return Path.Combine(baseDir, fileName);
        }

        /// <summary>
        /// 加载ComboBox历史记录和默认值
        /// </summary>
        /// <param name="comboBox">ComboBox控件</param>
        /// <param name="historyKey">历史记录的唯一标识键</param>
        /// <param name="autoFillLatestValue">是否自动填充最近的值</param>
        /// <param name="defaultValuesFilePath">下拉列表默认值文件路径，一行为一个值。如果为null或空，则不使用默认值功能</param>
        private static void LoadComboBoxHistory(ComboBox comboBox, string historyKey, bool autoFillLatestValue, string defaultValuesFilePath = null)
        {
            string historyFilePath = GetComboBoxHistoryFilePath(historyKey);

            // 使用HashSet来避免重复项，同时保持插入顺序
            List<string> allItems = new List<string>();
            HashSet<string> addedItems = new HashSet<string>();

            // 首先加载历史记录
            if (File.Exists(historyFilePath))
            {
                try
                {
                    // 读取历史记录
                    string[] historyItems = ETFile.ReadAllLinesWithEncoding(historyFilePath);

                    // 将每个非空的历史项添加到列表（历史值在前面）
                    foreach (string item in historyItems)
                    {
                        string trimmedItem = item?.Trim();
                        if (!string.IsNullOrWhiteSpace(trimmedItem) && !addedItems.Contains(trimmedItem))
                        {
                            allItems.Add(trimmedItem);
                            addedItems.Add(trimmedItem);
                        }
                    }
                }
                catch (Exception ex)
                {
                    ETLogManager.Error(typeof(ETForm), $"加载ComboBox历史记录失败: {historyKey}", ex);
                }
            }

            // 然后加载默认值文件（如果指定了）
            if (!string.IsNullOrWhiteSpace(defaultValuesFilePath) && File.Exists(defaultValuesFilePath))
            {
                try
                {
                    // 读取默认值文件，使用智能编码检测
                    string[] defaultValues = ETFile.ReadAllLinesWithEncoding(defaultValuesFilePath);

                    // 将每个非空的默认值添加到列表（默认值在后面，且不重复）
                    foreach (string item in defaultValues)
                    {
                        string trimmedItem = item?.Trim();
                        // 跳过注释行（以#开头的行）
                        if (!string.IsNullOrWhiteSpace(trimmedItem) &&
                            !trimmedItem.StartsWith("#") &&
                            !addedItems.Contains(trimmedItem))
                        {
                            allItems.Add(trimmedItem);
                            addedItems.Add(trimmedItem);
                        }
                    }
                }
                catch (Exception ex)
                {
                    ETLogManager.Error(typeof(ETForm), $"加载ComboBox默认值文件失败: {defaultValuesFilePath}", ex);
                }
            }

            // 清空下拉列表并添加所有项
            comboBox.Items.Clear();
            foreach (string item in allItems)
            {
                comboBox.Items.Add(item);
            }

            // 只有在autoFillLatestValue为true且有项目时才填充最近的值（优先使用历史记录中的第一项）
            if (autoFillLatestValue && comboBox.Items.Count > 0)
            {
                comboBox.Text = comboBox.Items[0].ToString();
            }
        }

        /// <summary>
        /// 加载ComboBox历史记录（向后兼容性方法）
        /// </summary>
        /// <param name="comboBox">ComboBox控件</param>
        /// <param name="historyKey">历史记录的唯一标识键</param>
        /// <param name="autoFillLatestValue">是否自动填充最近的值</param>
        private static void LoadComboBoxHistory(ComboBox comboBox, string historyKey, bool autoFillLatestValue)
        {
            // 调用新的重载方法，不使用默认值文件
            LoadComboBoxHistory(comboBox, historyKey, autoFillLatestValue, null);
        }

        /// <summary>
        /// 保存ComboBox历史记录
        /// </summary>
        /// <param name="comboBox">ComboBox控件</param>
        /// <param name="historyKey">历史记录的唯一标识键</param>
        /// <param name="maxHistoryCount">最大历史记录数量</param>
        private static void SaveComboBoxHistory(ComboBox comboBox, string historyKey, int maxHistoryCount)
        {
            if (string.IsNullOrWhiteSpace(comboBox.Text))
                return;

            try
            {
                // 获取当前所有项并转为List以便操作
                List<string> historyItems = new List<string>();
                foreach (object item in comboBox.Items)
                {
                    historyItems.Add(item.ToString());
                }

                string currentText = comboBox.Text.Trim();

                // 如果当前文本已存在，先移除
                historyItems.Remove(currentText);

                // 将新文本添加到最前面
                historyItems.Insert(0, currentText);

                // 限制历史记录数量
                if (historyItems.Count > maxHistoryCount)
                {
                    historyItems = historyItems.Take(maxHistoryCount).ToList();
                }

                // 保存当前选中的文本
                string selectedText = comboBox.Text;

                // 临时禁用事件处理，防止死循环
                bool eventsEnabled = comboBox.Enabled;
                try
                {
                    // 通过设置Tag来标记正在更新，避免递归调用
                    object originalTag = comboBox.Tag;
                    comboBox.Tag = "UPDATING_HISTORY";

                    // 更新ComboBox
                    comboBox.Items.Clear();
                    foreach (string historyItem in historyItems)
                    {
                        comboBox.Items.Add(historyItem);
                    }

                    // 恢复当前选中的文本
                    comboBox.Text = selectedText;

                    // 清除文本选择状态，避免全选高亮显示
                    comboBox.SelectionStart = selectedText?.Length ?? 0;
                    comboBox.SelectionLength = 0;

                    // 恢复原始Tag
                    comboBox.Tag = originalTag;
                }
                finally
                {
                    // 确保状态恢复
                }

                // 保存到文件
                SaveComboBoxHistoryToFile(historyItems, historyKey);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(typeof(ETForm), $"保存ComboBox历史记录失败: {historyKey}", ex);
            }
        }

        /// <summary>
        /// 将历史记录保存到文件
        /// </summary>
        /// <param name="historyItems">历史记录列表</param>
        /// <param name="historyKey">历史记录的唯一标识键</param>
        private static void SaveComboBoxHistoryToFile(List<string> historyItems, string historyKey)
        {
            try
            {
                string historyFilePath = GetComboBoxHistoryFilePath(historyKey);
                File.WriteAllLines(historyFilePath, historyItems);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(typeof(ETForm), $"保存ComboBox历史记录到文件失败: {historyKey}", ex);
            }
        }

        #endregion 操作配置文件

        #region 菜单

        /// <summary>
        /// 根据标题,设置菜单隐藏/显示
        /// </summary>
        /// <param name="controls"></param>
        /// <param name="caption"></param>
        /// <param name="visible"></param>
        /// <param name="onlyOne"></param>
        /// <returns></returns>
        public static void ShowHide菜单子项(
            CommandBarControls controls,
            string[] caption,
            bool visible,
            bool onlyOne = false)
        {
            foreach (CommandBarControl tmp in controls)
            {
                if (caption.Any(subStr => tmp.Caption.Trim() == subStr.Trim() || tmp.accName.Trim() == subStr.Trim()))
                {
                    tmp.Visible = visible;
                    if (onlyOne)
                        return;
                }
            }
        }

        /// <summary>
        /// 根据标题,设置菜单隐藏/显
        /// </summary>
        /// <param name="caption"></param>
        /// <param name="visible"></param>
        /// <returns></returns>
        public static void ShowHide菜单子项(string[] caption, bool visible)
        {
            foreach (CommandBarControl tmp in XlApp.CommandBars["cell"].Controls)
                if (caption.Any(subStr => tmp.Caption == subStr.Trim() || tmp.accName == subStr.Trim()))
                    tmp.Visible = visible;
        }

        /// <summary>
        /// 添加子菜单项
        /// </summary>
        /// <param name="controls">该字菜单的父菜单容器</param>
        /// <param name="menuCaption"></param>
        /// <param name="beginGroup"></param>
        /// <param name="group"></param>
        /// <param name="clickEventHandler"></param>
        /// <param name="index"></param>
        /// <param name="visible"></param>
        /// <param name="check"></param>
        /// <param name="faceId"></param>
        /// <returns></returns>
        public static CommandBarButton AddMenuButton(
            CommandBarControls controls,
            string menuCaption,
            string group,
            _CommandBarButtonEvents_ClickEventHandler clickEventHandler,
            int index,
            bool beginGroup,
            bool visible,
            bool check,
            int faceId = 0)
        {
            string menuTag = $"{group}:{menuCaption}";
            if (check)
            {
                CommandBarButton foundMenu = (CommandBarButton)
                    controls.Parent.FindControl(MsoControlType.msoControlButton, Type.Missing, menuTag, true, true);
                if (foundMenu != null)
                    foundMenu.Delete(true);
            }

            if (index <= 1)
                index = 1;
            if (index >= 100)
                index = controls.Count + 1;

            CommandBarButton btn = (CommandBarButton)
                controls.Add(MsoControlType.msoControlButton, Type.Missing, Type.Missing, index, true); //添加自己的菜单项

            btn.Caption = menuCaption;
            btn.TooltipText = menuTag;
            btn.Style = MsoButtonStyle.msoButtonIconAndCaption;
            btn.Click += clickEventHandler;
            btn.Visible = visible;
            btn.BeginGroup = beginGroup;
            if (faceId > 0)
                btn.FaceId = faceId;
            return btn;
        }

        /// <summary>
        /// 添加子菜单项
        /// </summary>
        /// <param name="controls">该字菜单的父菜单容器</param>
        /// <param name="menuCaption"></param>
        /// <param name="beginGroup"></param>
        /// <param name="group"></param>
        /// <param name="index"></param>
        /// <param name="visible"></param>
        /// <param name="check"></param>
        /// <returns></returns>
        public static CommandBarPopup AddPopupButton(
            CommandBarControls controls,
            string menuCaption,
            string group,
            int index,
            bool beginGroup,
            bool visible,
            bool check)
        {
            string menuTag = $"{group}:{menuCaption}";
            if (check)
            {
                CommandBarPopup foundMenu = (CommandBarPopup)
                    controls.Parent.FindControl(MsoControlType.msoControlPopup, Type.Missing, menuTag, true, true);
                if (foundMenu != null)
                    foundMenu.Delete(true);
            }

            if (index <= 1)
                index = 1;
            if (index >= 100)
                index = controls.Count;
            ;
            CommandBarPopup popup = (CommandBarPopup)
                controls.Add(MsoControlType.msoControlPopup, Type.Missing, Type.Missing, index, true); //添加自己的菜单项
            popup.Caption = menuCaption;
            popup.TooltipText = menuTag;
            popup.Visible = visible;
            popup.BeginGroup = beginGroup;
            return popup;
        }

        /// <summary>
        /// 查找Index
        /// </summary>
        /// <param name="commandBar">该字菜单的父菜单容器</param>
        /// <param name="menuCaption"></param>
        /// <returns></returns>
        public static int FindMenuIndex(CommandBar commandBar, string menuCaption)
        {
            if (commandBar == null || string.IsNullOrWhiteSpace(menuCaption))
                return 0;
            string menu = menuCaption.Trim();
            foreach (CommandBarControl subControl in commandBar.Controls)
                if (subControl.Caption == menu || subControl.accName == menu)
                    return subControl.Index;
            return 0;
        }

        #endregion 菜单

        #region Log

        /// <summary>
        /// 向TextBox中写入日志或追加文本
        /// </summary>
        /// <param name="textBox">TextBox控件</param>
        /// <param name="text">要写入的文本</param>
        /// <param name="newLine">是否换行</param>
        public static void WriteLog(this TextBox textBox, string text, bool newLine = true)
        {
            if (textBox == null)
                return;

            try
            {
                textBox?.Invoke(
                new EventHandler(
                    delegate
                    {
                        if (newLine && !string.IsNullOrEmpty(textBox.Text))
                            textBox.AppendText("\r\n");
                        textBox.AppendText(text);
                        textBox.ScrollBars = ScrollBars.Vertical;
                        textBox.ScrollToCaret();
                    }));
            }
            catch (Exception e)
            {
                Debug.Print(e.Message);
                return;
            }
        }

        // 保持向后兼容性
        public static void AppendTextInvoke(this TextBox textBox, string text, bool newLine = true)
        { WriteLog(textBox, text, newLine); }

        #endregion Log

        #region DllImport

        public const int WS_CHILD = 0x40000000; // 子窗体风格
        public const int WS_OVERLAPPEDWINDOW = 0xCF0000; // 标准窗体风格
        public const int WS_EX_NOACTIVATE = 0x08000000; // 防止窗体激活风格
        public const int WS_EX_APPWINDOW = 0x00040000; // 强制窗体在任务栏上显示
        public const int WS_EX_TOOLWINDOW = 0x00000080; // 工具窗体风格
        public const int GWL_STYLE = -16; // 用于获取和设置窗体风格
        public const int GWL_EXSTYLE = -20; // 用于获取和设置窗体扩展风格

        public const int WS_OVERLAPPED = 0x00000000;
        public const int WS_CAPTION = 0x00C00000; // WS_BORDER | WS_DLGFRAME
        public const int WS_SYSMENU = 0x00080000;
        public const int WS_THICKFRAME = 0x00040000; // WS_SIZEBOX
        public const int WS_MINIMIZEBOX = 0x00020000;
        public const int WS_MAXIMIZEBOX = 0x00010000;

        // 在32位系统中用SetWindowLong，在64位系统中使用SetWindowLongPtr
        [DllImport("user32.dll", EntryPoint = "SetWindowLong")]
        private static extern int SetWindowLong32(IntPtr hWnd, int nIndex, int dwNewLong);

        [DllImport("user32.dll", EntryPoint = "SetWindowLongPtr")]
        private static extern IntPtr SetWindowLongPtr64(IntPtr hWnd, int nIndex, IntPtr dwNewLong);

        // 用于设置父窗口的函数
        [DllImport("user32.dll", SetLastError = true)]
        public static extern IntPtr SetParent(IntPtr hWndChild, IntPtr hWndNewParent);

        // 不同系统位数下的API声明
        [DllImport("user32.dll", EntryPoint = "GetWindowLong")]
        private static extern int GetWindowLong32(IntPtr hWnd, int nIndex);

        [DllImport("user32.dll", EntryPoint = "GetWindowLongPtr")]
        private static extern IntPtr GetWindowLongPtr64(IntPtr hWnd, int nIndex);

        // 根据系统位数选择正确的函数指针版本
        private static IntPtr GetWindowLongPtr(IntPtr hWnd, int nIndex)
        {
            if (hWnd == IntPtr.Zero)
                return IntPtr.Zero;
            return IntPtr.Size == 8 ? GetWindowLongPtr64(hWnd, nIndex) : new IntPtr(GetWindowLong32(hWnd, nIndex));
        }

        // 根据系统位数选择正确的函数指针版本
        public static IntPtr SetWindowLongPtr(IntPtr hWnd, int nIndex, IntPtr dwNewLong)
        {
            if (hWnd == IntPtr.Zero)
                return IntPtr.Zero;
            return IntPtr.Size == 8
                ? SetWindowLongPtr64(hWnd, nIndex, dwNewLong)
                : new IntPtr(SetWindowLong32(hWnd, nIndex, dwNewLong.ToInt32()));
        }

        #endregion DllImport

        #region Win32 API Wrappers

        /// <summary>
        /// 查找窗口
        /// </summary>
        public static IntPtr FindWindow(string lpClassName, string lpWindowName)
        {
            return ETFormNative.FindWindow(lpClassName, lpWindowName);
        }

        /// <summary>
        /// 启用或禁用窗口
        /// </summary>
        public static bool EnableWindow(IntPtr hWnd, bool enable)
        {
            return ETFormNative.EnableWindow(hWnd, enable);
        }

        #endregion Win32 API Wrappers

        #region TextBox历史记录管理

        /// <summary>
        /// TextBox历史记录数据模型
        /// </summary>
        public class TextBoxHistoryData
        {
            /// <summary>
            /// 历史记录列表，最新的记录在最前面
            /// </summary>
            public List<string> History { get; set; } = new List<string>();

            /// <summary>
            /// 最大历史记录数量
            /// </summary>
            public int MaxHistoryCount { get; set; } = 10;

            /// <summary>
            /// 创建时间
            /// </summary>
            public DateTime CreatedTime { get; set; } = DateTime.Now;

            /// <summary>
            /// 最后更新时间
            /// </summary>
            public DateTime LastUpdatedTime { get; set; } = DateTime.Now;
        }

        /// <summary>
        /// 绑定TextBox控件和历史记录管理，自动生成historyKey（推荐使用）
        /// </summary>
        /// <param name="textBox">要绑定的TextBox控件</param>
        /// <param name="maxHistoryCount">最大历史记录数量，默认10</param>
        /// <param name="autoFillLatestValue">是否自动填充最近的值，默认true</param>
        /// <remarks>
        /// 此方法会：
        /// 1. 自动根据窗体类名和控件名生成唯一的historyKey
        /// 2. 为TextBox添加右键菜单，包含：复制、复制所有、清空、保存、历史功能
        /// 3. 历史记录保存在 Data\ET\Cache\ETForm\TextBoxHistory\ 目录下
        /// 4. 文件名格式：{FormClassName}_{ControlName}.json
        /// 5. 使用JSON格式保存，支持最多指定数量的历史记录
        /// 6. 最新的记录会自动排在最前面
        /// </remarks>
        public static void BindTextBox(TextBox textBox, int maxHistoryCount = 10, bool autoFillLatestValue = true)
        {
            if (textBox == null)
                return;

            try
            {
                // 自动生成historyKey
                string historyKey = GenerateTextBoxHistoryKey(textBox);

                // 调用带historyKey的重载方法
                BindTextBox(textBox, historyKey, maxHistoryCount, autoFillLatestValue);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(typeof(ETForm), "绑定TextBox历史记录时发生错误（自动生成historyKey）", ex);
            }
        }

        /// <summary>
        /// 绑定TextBox控件和历史记录管理，参照BindComboBox的实现方式
        /// </summary>
        /// <param name="textBox">要绑定的TextBox控件</param>
        /// <param name="historyKey">历史记录的唯一标识键，用于生成历史记录文件名</param>
        /// <param name="maxHistoryCount">最大历史记录数量，默认10</param>
        /// <param name="autoFillLatestValue">是否自动填充最近的值，默认true</param>
        /// <remarks>
        /// 此方法会：
        /// 1. 为TextBox添加右键菜单，包含：复制、复制所有、清空、保存、历史功能
        /// 2. 历史记录保存在 Data\ET\Cache\ETForm\TextBoxHistory\ 目录下
        /// 3. 文件名格式：{historyKey}.json
        /// 4. 使用JSON格式保存，支持最多指定数量的历史记录
        /// 5. 最新的记录会自动排在最前面
        /// </remarks>
        public static void BindTextBox(TextBox textBox, string historyKey, int maxHistoryCount = 10, bool autoFillLatestValue = true)
        {
            if (textBox == null || string.IsNullOrWhiteSpace(historyKey))
                return;

            try
            {
                // 加载历史记录并自动填充最新值（如果需要）
                LoadTextBoxHistory(textBox, historyKey, autoFillLatestValue);

                // 创建并设置右键菜单
                CreateTextBoxContextMenu(textBox, historyKey, maxHistoryCount);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(typeof(ETForm), "绑定TextBox历史记录时发生错误", ex);
            }
        }

        /// <summary>
        /// 自动生成TextBox历史记录的唯一标识键，参照ComboBox的实现方式
        /// </summary>
        /// <param name="textBox">TextBox控件</param>
        /// <returns>生成的唯一标识键</returns>
        private static string GenerateTextBoxHistoryKey(TextBox textBox)
        {
            try
            {
                // 获取当前控件所在窗体的类名
                string formClassName = textBox.FindForm()?.GetType().Name ?? "Unknown";

                // 获取控件名称，如果为空则使用默认名称
                string controlName = string.IsNullOrWhiteSpace(textBox.Name) ? "TextBox" : textBox.Name;

                // 使用窗体类名和控件名生成唯一标识键
                return $"{formClassName}_{controlName}";
            }
            catch (Exception ex)
            {
                ETLogManager.Error(typeof(ETForm), "生成TextBox历史记录键时发生错误", ex);
                // 返回一个默认的键名
                return $"Unknown_TextBox_{DateTime.Now.Ticks}";
            }
        }

        /// <summary>
        /// 获取TextBox历史记录文件路径
        /// </summary>
        /// <param name="historyKey">历史记录的唯一标识键</param>
        /// <returns>历史记录文件的完整路径</returns>
        private static string GetTextBoxHistoryFilePath(string historyKey)
        {
            string baseDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "ET", "Cache", "ETForm", "TextBoxHistory");

            // 确保目录存在
            if (!Directory.Exists(baseDir))
            {
                Directory.CreateDirectory(baseDir);
            }

            // 使用historyKey生成文件名
            string fileName = $"{historyKey}.json";

            return Path.Combine(baseDir, fileName);
        }

        /// <summary>
        /// 加载TextBox历史记录
        /// </summary>
        /// <param name="textBox">TextBox控件</param>
        /// <param name="historyKey">历史记录的唯一标识键</param>
        /// <param name="autoFillLatestValue">是否自动填充最近的值</param>
        private static void LoadTextBoxHistory(TextBox textBox, string historyKey, bool autoFillLatestValue)
        {
            try
            {
                // 加载历史记录数据
                var historyData = LoadTextBoxHistoryData(historyKey);

                // 只有在autoFillLatestValue为true且有历史记录时才填充最近的值
                if (autoFillLatestValue && historyData?.History?.Count > 0)
                {
                    textBox.Text = historyData.History[0];
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(typeof(ETForm), $"加载TextBox历史记录失败: {historyKey}", ex);
            }
        }

        /// <summary>
        /// 保存TextBox历史记录
        /// </summary>
        /// <param name="textBox">TextBox控件</param>
        /// <param name="historyKey">历史记录的唯一标识键</param>
        /// <param name="maxHistoryCount">最大历史记录数量</param>
        private static void SaveTextBoxHistory(TextBox textBox, string historyKey, int maxHistoryCount)
        {
            if (string.IsNullOrWhiteSpace(textBox.Text))
                return;

            try
            {
                string historyFilePath = GetTextBoxHistoryFilePath(historyKey);
                TextBoxHistoryData historyData;

                // 如果文件存在，加载现有数据；否则创建新数据
                if (File.Exists(historyFilePath))
                {
                    try
                    {
                        string jsonContent = File.ReadAllText(historyFilePath);
                        historyData = JsonConvert.DeserializeObject<TextBoxHistoryData>(jsonContent) ?? new TextBoxHistoryData();
                    }
                    catch
                    {
                        historyData = new TextBoxHistoryData();
                    }
                }
                else
                {
                    historyData = new TextBoxHistoryData();
                }

                string currentText = textBox.Text.Trim();

                // 如果当前文本已存在，先移除
                historyData.History.Remove(currentText);

                // 将新文本添加到最前面
                historyData.History.Insert(0, currentText);

                // 限制历史记录数量
                if (historyData.History.Count > maxHistoryCount)
                {
                    historyData.History = historyData.History.Take(maxHistoryCount).ToList();
                }

                // 更新时间和最大数量
                historyData.MaxHistoryCount = maxHistoryCount;
                historyData.LastUpdatedTime = DateTime.Now;

                // 保存到文件
                string jsonToSave = JsonConvert.SerializeObject(historyData, Formatting.Indented);
                File.WriteAllText(historyFilePath, jsonToSave);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(typeof(ETForm), $"保存TextBox历史记录失败: {historyKey}", ex);
            }
        }

        /// <summary>
        /// 为TextBox创建右键菜单
        /// </summary>
        /// <param name="textBox">TextBox控件</param>
        /// <param name="historyKey">历史记录的唯一标识键</param>
        /// <param name="maxHistoryCount">最大历史记录数量</param>
        private static void CreateTextBoxContextMenu(TextBox textBox, string historyKey, int maxHistoryCount)
        {
            try
            {
                // 创建右键菜单
                ContextMenuStrip contextMenu = new ContextMenuStrip();

                // 复制菜单项（放在最上面）
                ToolStripMenuItem copyMenuItem = new ToolStripMenuItem("复制");
                copyMenuItem.Click += (sender, e) =>
                {
                    if (textBox.SelectionLength > 0)
                    {
                        Clipboard.SetText(textBox.SelectedText);
                    }
                    else
                    {
                        MessageBox.Show("请先选择要复制的文本", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                };
                contextMenu.Items.Add(copyMenuItem);

                // 复制所有菜单项
                ToolStripMenuItem copyAllMenuItem = new ToolStripMenuItem("复制所有");
                copyAllMenuItem.Click += (sender, e) =>
                {
                    if (!string.IsNullOrEmpty(textBox.Text))
                    {
                        Clipboard.SetText(textBox.Text);
                        MessageBox.Show("已复制所有内容到剪贴板", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        MessageBox.Show("文本框为空，无内容可复制", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                };
                contextMenu.Items.Add(copyAllMenuItem);

                // 清空菜单项
                ToolStripMenuItem clearMenuItem = new ToolStripMenuItem("清空");
                clearMenuItem.Click += (sender, e) =>
                {
                    if (MessageBox.Show("确定要清空文本框内容吗？", "确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                    {
                        textBox.Clear();
                    }
                };
                contextMenu.Items.Add(clearMenuItem);

                // 分隔线（分隔复制/清空 和 保存/历史功能）
                contextMenu.Items.Add(new ToolStripSeparator());

                // 保存菜单项（移到下方）
                ToolStripMenuItem saveMenuItem = new ToolStripMenuItem("保存");
                saveMenuItem.Click += (sender, e) =>
                {
                    SaveTextBoxHistory(textBox, historyKey, maxHistoryCount);
                    MessageBox.Show("历史记录已保存", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                };
                contextMenu.Items.Add(saveMenuItem);

                // 历史记录菜单项（动态生成子菜单，移到下方）
                ToolStripMenuItem historyMenuItem = new ToolStripMenuItem("历史");
                historyMenuItem.DropDownOpening += (sender, e) =>
                {
                    // 清空现有子菜单
                    historyMenuItem.DropDownItems.Clear();

                    // 加载历史记录
                    var historyData = LoadTextBoxHistoryData(historyKey);
                    if (historyData?.History?.Count > 0)
                    {
                        // 添加历史记录项
                        foreach (string historyItem in historyData.History)
                        {
                            // 优化菜单显示文本：如果内容超过50个字符则截取
                            string displayText = TruncateTextForMenu(historyItem, 50);

                            ToolStripMenuItem historySubItem = new ToolStripMenuItem(displayText);
                            historySubItem.Click += (subSender, subE) =>
                            {
                                textBox.Text = historyItem; // 点击时填入完整内容
                            };

                            // 如果文本被截取了，设置工具提示显示完整内容
                            if (displayText != historyItem)
                            {
                                historySubItem.ToolTipText = historyItem;
                            }

                            historyMenuItem.DropDownItems.Add(historySubItem);
                        }

                        // 添加分隔线
                        historyMenuItem.DropDownItems.Add(new ToolStripSeparator());

                        // 添加清除历史菜单项
                        ToolStripMenuItem clearHistoryItem = new ToolStripMenuItem("清除历史");
                        clearHistoryItem.Click += (subSender, subE) =>
                        {
                            if (MessageBox.Show("确定要清除所有历史记录吗？", "确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                            {
                                ClearTextBoxHistory(historyKey);
                                MessageBox.Show("历史记录已清除", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            }
                        };
                        historyMenuItem.DropDownItems.Add(clearHistoryItem);
                    }
                    else
                    {
                        ToolStripMenuItem noHistoryItem = new ToolStripMenuItem("无历史记录");
                        noHistoryItem.Enabled = false;
                        historyMenuItem.DropDownItems.Add(noHistoryItem);

                        // 即使没有历史记录，也提供清除历史选项（虽然没有实际作用）
                        historyMenuItem.DropDownItems.Add(new ToolStripSeparator());
                        ToolStripMenuItem clearHistoryItem = new ToolStripMenuItem("清除历史");
                        clearHistoryItem.Enabled = false;
                        historyMenuItem.DropDownItems.Add(clearHistoryItem);
                    }
                };
                contextMenu.Items.Add(historyMenuItem);

                // 设置右键菜单
                textBox.ContextMenuStrip = contextMenu;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(typeof(ETForm), "创建TextBox右键菜单时发生错误", ex);
            }
        }

        /// <summary>
        /// 加载TextBox历史记录数据
        /// </summary>
        /// <param name="historyKey">历史记录的唯一标识键</param>
        /// <returns>历史记录数据对象，失败返回null</returns>
        private static TextBoxHistoryData LoadTextBoxHistoryData(string historyKey)
        {
            try
            {
                string historyFilePath = GetTextBoxHistoryFilePath(historyKey);

                if (File.Exists(historyFilePath))
                {
                    string jsonContent = File.ReadAllText(historyFilePath);
                    return JsonConvert.DeserializeObject<TextBoxHistoryData>(jsonContent);
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(typeof(ETForm), $"加载TextBox历史记录数据失败: {historyKey}", ex);
            }

            return null;
        }

        /// <summary>
        /// 清除TextBox历史记录
        /// </summary>
        /// <param name="historyKey">历史记录的唯一标识键</param>
        private static void ClearTextBoxHistory(string historyKey)
        {
            try
            {
                string historyFilePath = GetTextBoxHistoryFilePath(historyKey);

                if (File.Exists(historyFilePath))
                {
                    File.Delete(historyFilePath);
                    ETLogManager.Info(typeof(ETForm), $"TextBox历史记录已清除: {historyKey}");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(typeof(ETForm), $"清除TextBox历史记录失败: {historyKey}", ex);
            }
        }

        /// <summary>
        /// 截取文本用于菜单显示，支持中英文混合字符计算
        /// </summary>
        /// <param name="text">原始文本</param>
        /// <param name="maxLength">最大字符长度（中文字符数）</param>
        /// <returns>截取后的文本，如果被截取会添加"..."后缀</returns>
        /// <remarks>
        /// 计算规则：
        /// - 中文字符（包括中文标点）：占1个字符长度
        /// - 英文字符（包括数字、英文标点）：占0.5个字符长度
        /// - 如果文本被截取，会在末尾添加"..."
        /// </remarks>
        private static string TruncateTextForMenu(string text, int maxLength)
        {
            if (string.IsNullOrEmpty(text))
                return text;

            try
            {
                double currentLength = 0;
                int truncateIndex = 0;

                for (int i = 0; i < text.Length; i++)
                {
                    char c = text[i];

                    // 判断是否为中文字符（包括中文标点）
                    if (IsChinese(c))
                    {
                        currentLength += 1.0; // 中文字符占1个长度
                    }
                    else
                    {
                        currentLength += 0.5; // 英文字符占0.5个长度
                    }

                    if (currentLength > maxLength)
                    {
                        truncateIndex = i;
                        break;
                    }

                    truncateIndex = i + 1;
                }

                // 如果需要截取
                if (truncateIndex < text.Length)
                {
                    return text.Substring(0, truncateIndex) + "...";
                }

                return text;
            }
            catch (Exception ex)
            {
                ETLogManager.Error(typeof(ETForm), "截取菜单文本时发生错误", ex);
                // 发生错误时返回原文本的简单截取
                return text.Length > maxLength ? text.Substring(0, maxLength) + "..." : text;
            }
        }

        /// <summary>
        /// 判断字符是否为中文字符（包括中文标点符号）
        /// </summary>
        /// <param name="c">要判断的字符</param>
        /// <returns>如果是中文字符返回true，否则返回false</returns>
        private static bool IsChinese(char c)
        {
            // 中文字符的Unicode范围
            return (c >= 0x4e00 && c <= 0x9fff) ||      // 中日韩统一表意文字
                   (c >= 0x3400 && c <= 0x4dbf) ||      // 中日韩统一表意文字扩展A
                   (c >= 0x20000 && c <= 0x2a6df) ||    // 中日韩统一表意文字扩展B
                   (c >= 0x2a700 && c <= 0x2b73f) ||    // 中日韩统一表意文字扩展C
                   (c >= 0x2b740 && c <= 0x2b81f) ||    // 中日韩统一表意文字扩展D
                   (c >= 0x3000 && c <= 0x303f) ||      // 中日韩符号和标点
                   (c >= 0xff00 && c <= 0xffef);        // 全角ASCII、全角标点
        }

        #endregion TextBox历史记录管理
    }
}